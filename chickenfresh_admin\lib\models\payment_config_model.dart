import 'package:cloud_firestore/cloud_firestore.dart';

class PaymentConfigModel {
  final String id;
  final bool isPhonePeEnabled;
  final String phonePeMerchantId;
  final String phonePeSaltKey;
  final int phonePeSaltIndex;
  final String phonePeBaseUrl;
  final bool isPhonePeTestMode;
  
  final bool isRazorpayEnabled;
  final String razorpayKeyId;
  final String razorpayKeySecret;
  final bool isRazorpayTestMode;
  
  final bool isPaytmEnabled;
  final String paytmMerchantId;
  final String paytmMerchantKey;
  final bool isPaytmTestMode;
  
  final bool isUpiEnabled;
  final String upiMerchantId;
  final String upiMerchantName;
  
  final bool isCodEnabled;
  final double codMinAmount;
  final double codMaxAmount;
  final double codCharges;
  
  final String defaultPaymentMethod;
  final bool isPaymentEnabled;
  final DateTime createdAt;
  final DateTime? updatedAt;

  PaymentConfigModel({
    required this.id,
    this.isPhonePeEnabled = false,
    this.phonePeMerchantId = '',
    this.phonePeSaltKey = '',
    this.phonePeSaltIndex = 1,
    this.phonePeBaseUrl = 'https://api-preprod.phonepe.com/apis/pg-sandbox',
    this.isPhonePeTestMode = true,
    
    this.isRazorpayEnabled = false,
    this.razorpayKeyId = '',
    this.razorpayKeySecret = '',
    this.isRazorpayTestMode = true,
    
    this.isPaytmEnabled = false,
    this.paytmMerchantId = '',
    this.paytmMerchantKey = '',
    this.isPaytmTestMode = true,
    
    this.isUpiEnabled = true,
    this.upiMerchantId = 'merchant@paytm',
    this.upiMerchantName = 'ChickenFresh',
    
    this.isCodEnabled = true,
    this.codMinAmount = 0.0,
    this.codMaxAmount = 5000.0,
    this.codCharges = 0.0,
    
    this.defaultPaymentMethod = 'upi',
    this.isPaymentEnabled = true,
    required this.createdAt,
    this.updatedAt,
  });

  factory PaymentConfigModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>? ?? {};
    return PaymentConfigModel(
      id: doc.id,
      isPhonePeEnabled: data['isPhonePeEnabled'] ?? false,
      phonePeMerchantId: data['phonePeMerchantId'] ?? '',
      phonePeSaltKey: data['phonePeSaltKey'] ?? '',
      phonePeSaltIndex: data['phonePeSaltIndex'] ?? 1,
      phonePeBaseUrl: data['phonePeBaseUrl'] ?? 'https://api-preprod.phonepe.com/apis/pg-sandbox',
      isPhonePeTestMode: data['isPhonePeTestMode'] ?? true,
      
      isRazorpayEnabled: data['isRazorpayEnabled'] ?? false,
      razorpayKeyId: data['razorpayKeyId'] ?? '',
      razorpayKeySecret: data['razorpayKeySecret'] ?? '',
      isRazorpayTestMode: data['isRazorpayTestMode'] ?? true,
      
      isPaytmEnabled: data['isPaytmEnabled'] ?? false,
      paytmMerchantId: data['paytmMerchantId'] ?? '',
      paytmMerchantKey: data['paytmMerchantKey'] ?? '',
      isPaytmTestMode: data['isPaytmTestMode'] ?? true,
      
      isUpiEnabled: data['isUpiEnabled'] ?? true,
      upiMerchantId: data['upiMerchantId'] ?? 'merchant@paytm',
      upiMerchantName: data['upiMerchantName'] ?? 'ChickenFresh',
      
      isCodEnabled: data['isCodEnabled'] ?? true,
      codMinAmount: (data['codMinAmount'] ?? 0.0).toDouble(),
      codMaxAmount: (data['codMaxAmount'] ?? 5000.0).toDouble(),
      codCharges: (data['codCharges'] ?? 0.0).toDouble(),
      
      defaultPaymentMethod: data['defaultPaymentMethod'] ?? 'upi',
      isPaymentEnabled: data['isPaymentEnabled'] ?? true,
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate(),
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'isPhonePeEnabled': isPhonePeEnabled,
      'phonePeMerchantId': phonePeMerchantId,
      'phonePeSaltKey': phonePeSaltKey,
      'phonePeSaltIndex': phonePeSaltIndex,
      'phonePeBaseUrl': phonePeBaseUrl,
      'isPhonePeTestMode': isPhonePeTestMode,
      
      'isRazorpayEnabled': isRazorpayEnabled,
      'razorpayKeyId': razorpayKeyId,
      'razorpayKeySecret': razorpayKeySecret,
      'isRazorpayTestMode': isRazorpayTestMode,
      
      'isPaytmEnabled': isPaytmEnabled,
      'paytmMerchantId': paytmMerchantId,
      'paytmMerchantKey': paytmMerchantKey,
      'isPaytmTestMode': isPaytmTestMode,
      
      'isUpiEnabled': isUpiEnabled,
      'upiMerchantId': upiMerchantId,
      'upiMerchantName': upiMerchantName,
      
      'isCodEnabled': isCodEnabled,
      'codMinAmount': codMinAmount,
      'codMaxAmount': codMaxAmount,
      'codCharges': codCharges,
      
      'defaultPaymentMethod': defaultPaymentMethod,
      'isPaymentEnabled': isPaymentEnabled,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': updatedAt != null ? Timestamp.fromDate(updatedAt!) : null,
    };
  }

  PaymentConfigModel copyWith({
    String? id,
    bool? isPhonePeEnabled,
    String? phonePeMerchantId,
    String? phonePeSaltKey,
    int? phonePeSaltIndex,
    String? phonePeBaseUrl,
    bool? isPhonePeTestMode,
    
    bool? isRazorpayEnabled,
    String? razorpayKeyId,
    String? razorpayKeySecret,
    bool? isRazorpayTestMode,
    
    bool? isPaytmEnabled,
    String? paytmMerchantId,
    String? paytmMerchantKey,
    bool? isPaytmTestMode,
    
    bool? isUpiEnabled,
    String? upiMerchantId,
    String? upiMerchantName,
    
    bool? isCodEnabled,
    double? codMinAmount,
    double? codMaxAmount,
    double? codCharges,
    
    String? defaultPaymentMethod,
    bool? isPaymentEnabled,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return PaymentConfigModel(
      id: id ?? this.id,
      isPhonePeEnabled: isPhonePeEnabled ?? this.isPhonePeEnabled,
      phonePeMerchantId: phonePeMerchantId ?? this.phonePeMerchantId,
      phonePeSaltKey: phonePeSaltKey ?? this.phonePeSaltKey,
      phonePeSaltIndex: phonePeSaltIndex ?? this.phonePeSaltIndex,
      phonePeBaseUrl: phonePeBaseUrl ?? this.phonePeBaseUrl,
      isPhonePeTestMode: isPhonePeTestMode ?? this.isPhonePeTestMode,
      
      isRazorpayEnabled: isRazorpayEnabled ?? this.isRazorpayEnabled,
      razorpayKeyId: razorpayKeyId ?? this.razorpayKeyId,
      razorpayKeySecret: razorpayKeySecret ?? this.razorpayKeySecret,
      isRazorpayTestMode: isRazorpayTestMode ?? this.isRazorpayTestMode,
      
      isPaytmEnabled: isPaytmEnabled ?? this.isPaytmEnabled,
      paytmMerchantId: paytmMerchantId ?? this.paytmMerchantId,
      paytmMerchantKey: paytmMerchantKey ?? this.paytmMerchantKey,
      isPaytmTestMode: isPaytmTestMode ?? this.isPaytmTestMode,
      
      isUpiEnabled: isUpiEnabled ?? this.isUpiEnabled,
      upiMerchantId: upiMerchantId ?? this.upiMerchantId,
      upiMerchantName: upiMerchantName ?? this.upiMerchantName,
      
      isCodEnabled: isCodEnabled ?? this.isCodEnabled,
      codMinAmount: codMinAmount ?? this.codMinAmount,
      codMaxAmount: codMaxAmount ?? this.codMaxAmount,
      codCharges: codCharges ?? this.codCharges,
      
      defaultPaymentMethod: defaultPaymentMethod ?? this.defaultPaymentMethod,
      isPaymentEnabled: isPaymentEnabled ?? this.isPaymentEnabled,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  // Helper methods
  bool get hasValidPhonePeConfig {
    return isPhonePeEnabled && 
           phonePeMerchantId.isNotEmpty && 
           phonePeSaltKey.isNotEmpty;
  }

  bool get hasValidRazorpayConfig {
    return isRazorpayEnabled && 
           razorpayKeyId.isNotEmpty && 
           razorpayKeySecret.isNotEmpty;
  }

  bool get hasValidPaytmConfig {
    return isPaytmEnabled && 
           paytmMerchantId.isNotEmpty && 
           paytmMerchantKey.isNotEmpty;
  }

  List<String> get enabledPaymentMethods {
    final methods = <String>[];
    if (isPhonePeEnabled && hasValidPhonePeConfig) methods.add('phonepe');
    if (isRazorpayEnabled && hasValidRazorpayConfig) methods.add('razorpay');
    if (isPaytmEnabled && hasValidPaytmConfig) methods.add('paytm');
    if (isUpiEnabled) methods.add('upi');
    if (isCodEnabled) methods.add('cod');
    return methods;
  }
}

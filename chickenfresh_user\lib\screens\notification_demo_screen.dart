import 'package:flutter/material.dart';
import '../services/notification_service.dart';
import '../widgets/chicken_watermark.dart';

class NotificationDemoScreen extends StatefulWidget {
  const NotificationDemoScreen({super.key});

  @override
  State<NotificationDemoScreen> createState() => _NotificationDemoScreenState();
}

class _NotificationDemoScreenState extends State<NotificationDemoScreen> {
  String? _fcmToken;

  @override
  void initState() {
    super.initState();
    _loadFCMToken();
  }

  Future<void> _loadFCMToken() async {
    final token = await NotificationService.getFCMToken();
    setState(() {
      _fcmToken = token;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('Notification Demo'),
        backgroundColor: Colors.red.shade400,
        foregroundColor: Colors.white,
      ),
      body: ChickenImageWatermark(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // FCM Token Display
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.blue.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.blue.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.token, color: Colors.blue.shade600),
                        const SizedBox(width: 8),
                        const Text(
                          'FCM Token',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey.shade300),
                      ),
                      child: Text(
                        _fcmToken ?? 'Loading...',
                        style: const TextStyle(
                          fontSize: 12,
                          fontFamily: 'monospace',
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              const SizedBox(height: 24),

              // In-App Notifications Section
              _buildSection(
                'In-App Notifications',
                'These appear as overlay notifications within the app',
                Icons.notifications_active,
                Colors.green,
                [
                  _buildDemoButton(
                    'Success Notification',
                    'Show a success message',
                    Colors.green,
                    () => NotificationService.showSuccessNotification(
                      'Operation completed successfully!',
                    ),
                  ),
                  _buildDemoButton(
                    'Error Notification',
                    'Show an error message',
                    Colors.red,
                    () => NotificationService.showErrorNotification(
                      'Something went wrong. Please try again.',
                    ),
                  ),
                  _buildDemoButton(
                    'Info Notification',
                    'Show an informational message',
                    Colors.blue,
                    () => NotificationService.showInfoNotification(
                      'Here\'s some useful information for you.',
                    ),
                  ),
                  _buildDemoButton(
                    'Custom In-App',
                    'Show a custom in-app notification',
                    Colors.purple,
                    () => NotificationService.showInAppNotification(
                      title: 'Custom Notification',
                      message: 'This is a custom in-app notification with tap action!',
                      type: 'custom',
                      onTap: () {
                        ScaffoldMessenger.of(context).showSnackBar(
                          const SnackBar(content: Text('Notification tapped!')),
                        );
                      },
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Local Push Notifications Section
              _buildSection(
                'Local Push Notifications',
                'These appear in the system notification tray',
                Icons.notifications,
                Colors.orange,
                [
                  _buildDemoButton(
                    'Order Update',
                    'Simulate an order status update',
                    Colors.green,
                    () => NotificationService.showOrderNotification(
                      orderId: 'ORD${DateTime.now().millisecondsSinceEpoch}',
                      status: 'confirmed',
                      message: 'Your order has been confirmed and is being prepared!',
                    ),
                  ),
                  _buildDemoButton(
                    'Delivery Update',
                    'Simulate a delivery status update',
                    Colors.orange,
                    () => NotificationService.showDeliveryNotification(
                      orderId: 'ORD${DateTime.now().millisecondsSinceEpoch}',
                      status: 'out for delivery',
                      estimatedTime: '15 minutes',
                    ),
                  ),
                  _buildDemoButton(
                    'Special Offer',
                    'Show a promotional notification',
                    Colors.purple,
                    () => NotificationService.showOfferNotification(
                      title: '🔥 Flash Sale!',
                      description: 'Get 50% off on all chicken products. Limited time offer!',
                      offerId: 'FLASH50',
                    ),
                  ),
                  _buildDemoButton(
                    'New Product',
                    'Announce a new product',
                    Colors.red,
                    () => NotificationService.showProductNotification(
                      productName: '🍗 Premium Chicken Breast',
                      message: 'Fresh premium chicken breast now available in your area!',
                      productId: 'PROD123',
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 24),

              // Notification Management Section
              _buildSection(
                'Notification Management',
                'Manage and control notifications',
                Icons.settings,
                Colors.grey,
                [
                  _buildDemoButton(
                    'Clear All Notifications',
                    'Remove all pending notifications',
                    Colors.red,
                    () async {
                      await NotificationService.cancelAllNotifications();
                      NotificationService.showInfoNotification(
                        'All notifications have been cleared!',
                      );
                    },
                  ),
                  _buildDemoButton(
                    'Subscribe to Offers',
                    'Subscribe to promotional notifications',
                    Colors.green,
                    () async {
                      await NotificationService.subscribeToTopic('offers');
                      NotificationService.showSuccessNotification(
                        'Subscribed to offer notifications!',
                      );
                    },
                  ),
                  _buildDemoButton(
                    'Unsubscribe from Offers',
                    'Unsubscribe from promotional notifications',
                    Colors.orange,
                    () async {
                      await NotificationService.unsubscribeFromTopic('offers');
                      NotificationService.showInfoNotification(
                        'Unsubscribed from offer notifications!',
                      );
                    },
                  ),
                ],
              ),
              const SizedBox(height: 32),

              // Instructions
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.amber.shade50,
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(color: Colors.amber.shade200),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Icon(Icons.info, color: Colors.amber.shade700),
                        const SizedBox(width: 8),
                        Text(
                          'How to Test',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                            color: Colors.amber.shade800,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    Text(
                      '• In-app notifications appear as overlays within the app\n'
                      '• Local notifications appear in the system notification tray\n'
                      '• Try minimizing the app and triggering notifications\n'
                      '• Check notification settings to customize preferences\n'
                      '• FCM token is used for remote push notifications',
                      style: TextStyle(
                        fontSize: 14,
                        color: Colors.amber.shade700,
                        height: 1.5,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(
    String title,
    String description,
    IconData icon,
    MaterialColor color,
    List<Widget> buttons,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.shade50,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color.shade600, size: 20),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      description,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.grey.shade600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: buttons,
          ),
        ],
      ),
    );
  }

  Widget _buildDemoButton(
    String title,
    String subtitle,
    MaterialColor color,
    VoidCallback onPressed,
  ) {
    return SizedBox(
      width: double.infinity,
      child: ElevatedButton(
        onPressed: onPressed,
        style: ElevatedButton.styleFrom(
          backgroundColor: color.shade50,
          foregroundColor: color.shade700,
          elevation: 0,
          padding: const EdgeInsets.all(16),
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(8),
            side: BorderSide(color: color.shade200),
          ),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: const TextStyle(
                fontSize: 12,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

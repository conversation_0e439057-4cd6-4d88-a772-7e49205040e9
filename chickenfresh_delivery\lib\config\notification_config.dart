class NotificationConfig {
  // Notification channel configuration
  static const String channelId = 'delivery_notifications';
  static const String channelName = 'Delivery Notifications';
  static const String channelDescription = 'Notifications for delivery persons';

  // Notification types
  static const String newOrderType = 'new_order';
  static const String orderUpdateType = 'order_update';
  static const String paymentReminderType = 'payment_reminder';
  static const String systemMessageType = 'system_message';

  // Notification priorities
  static const int highPriority = 2;
  static const int defaultPriority = 1;
  static const int lowPriority = 0;

  // Auto-dismiss durations
  static const Duration shortDuration = Duration(seconds: 3);
  static const Duration defaultDuration = Duration(seconds: 4);
  static const Duration longDuration = Duration(seconds: 6);

  // Sound and vibration settings
  static const bool enableSound = true;
  static const bool enableVibration = true;
  static const bool enableBadge = true;

  // Production settings
  static const bool enableDebugLogs = false;
  static const bool enableTestNotifications = false;
}

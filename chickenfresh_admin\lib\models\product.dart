import 'package:cloud_firestore/cloud_firestore.dart';

class Product {
  final String id;
  final String name;
  final double price;
  final double mrp;
  final String description;
  final String? imageUrl;
  final String category;
  final bool isAvailable;
  final int stockQuantity;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String createdBy;

  Product({
    required this.id,
    required this.name,
    required this.price,
    required this.mrp,
    required this.description,
    this.imageUrl,
    required this.category,
    this.isAvailable = true,
    this.stockQuantity = 0,
    required this.createdAt,
    required this.updatedAt,
    required this.createdBy,
  });

  // Factory constructor from Firestore document
  factory Product.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    // For backward compatibility, if MRP is missing, use price as MRP
    final price = _validateDouble(data['price'], 'price');
    final mrp = _validateDouble(data['mrp'], 'mrp', defaultValue: price);

    return Product(
      id: doc.id,
      name: _validateString(data['name'], 'name'),
      price: price,
      mrp: mrp,
      description: _validateString(data['description'], 'description'),
      imageUrl: data['imageUrl'] as String?,
      category: _validateString(data['category'], 'category', defaultValue: 'General'),
      isAvailable: data['isAvailable'] as bool? ?? true,
      stockQuantity: data['stockQuantity'] as int? ?? 0,
      createdAt: _validateTimestamp(data['createdAt'], 'createdAt'),
      updatedAt: _validateTimestamp(data['updatedAt'], 'updatedAt'),
      createdBy: _validateString(data['createdBy'], 'createdBy', defaultValue: 'system'),
    );
  }

  // Factory constructor from JSON
  factory Product.fromJson(Map<String, dynamic> json) {
    // For backward compatibility, if MRP is missing, use price as MRP
    final price = _validateDouble(json['price'], 'price');
    final mrp = _validateDouble(json['mrp'], 'mrp', defaultValue: price);

    return Product(
      id: json['id'] ?? '',
      name: _validateString(json['name'], 'name'),
      price: price,
      mrp: mrp,
      description: _validateString(json['description'], 'description'),
      imageUrl: json['imageUrl'] as String?,
      category: _validateString(json['category'], 'category', defaultValue: 'General'),
      isAvailable: json['isAvailable'] as bool? ?? true,
      stockQuantity: json['stockQuantity'] as int? ?? 0,
      createdAt: json['createdAt'] != null
          ? DateTime.parse(json['createdAt'])
          : DateTime.now(),
      updatedAt: json['updatedAt'] != null
          ? DateTime.parse(json['updatedAt'])
          : DateTime.now(),
      createdBy: _validateString(json['createdBy'], 'createdBy', defaultValue: 'system'),
    );
  }

  // Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'name': name,
      'price': price,
      'mrp': mrp,
      'description': description,
      'imageUrl': imageUrl,
      'category': category,
      'isAvailable': isAvailable,
      'stockQuantity': stockQuantity,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'createdBy': createdBy,
    };
  }

  // Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'price': price,
      'mrp': mrp,
      'description': description,
      'imageUrl': imageUrl,
      'category': category,
      'isAvailable': isAvailable,
      'stockQuantity': stockQuantity,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'createdBy': createdBy,
    };
  }

  // Create a copy with updated fields
  Product copyWith({
    String? id,
    String? name,
    double? price,
    double? mrp,
    String? description,
    String? imageUrl,
    String? category,
    bool? isAvailable,
    int? stockQuantity,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? createdBy,
  }) {
    return Product(
      id: id ?? this.id,
      name: name ?? this.name,
      price: price ?? this.price,
      mrp: mrp ?? this.mrp,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      category: category ?? this.category,
      isAvailable: isAvailable ?? this.isAvailable,
      stockQuantity: stockQuantity ?? this.stockQuantity,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      createdBy: createdBy ?? this.createdBy,
    );
  }

  // Validation methods
  static String _validateString(dynamic value, String fieldName, {String? defaultValue}) {
    if (value == null || value is! String || value.trim().isEmpty) {
      if (defaultValue != null) {
        return defaultValue;
      }
      throw ArgumentError('$fieldName is required and must be a non-empty string');
    }
    return value.trim();
  }

  static double _validateDouble(dynamic value, String fieldName, {double? defaultValue}) {
    if (value == null) {
      if (defaultValue != null) {
        return defaultValue;
      }
      throw ArgumentError('$fieldName is required');
    }
    if (value is int) {
      return value.toDouble();
    }
    if (value is double) {
      return value;
    }
    if (value is String) {
      final parsed = double.tryParse(value);
      if (parsed == null) {
        throw ArgumentError('$fieldName must be a valid number');
      }
      return parsed;
    }
    throw ArgumentError('$fieldName must be a number');
  }

  static DateTime _validateTimestamp(dynamic value, String fieldName) {
    if (value == null) {
      return DateTime.now();
    }
    if (value is Timestamp) {
      return value.toDate();
    }
    if (value is DateTime) {
      return value;
    }
    if (value is String) {
      try {
        return DateTime.parse(value);
      } catch (e) {
        throw ArgumentError('$fieldName must be a valid timestamp');
      }
    }
    throw ArgumentError('$fieldName must be a valid timestamp');
  }

  // Business logic methods
  bool get isInStock => stockQuantity > 0;
  bool get isLowStock => stockQuantity > 0 && stockQuantity <= 5;

  String get formattedPrice => '₹${price.toStringAsFixed(2)}';
  String get formattedMrp => '₹${mrp.toStringAsFixed(2)}';

  // Discount calculations
  double get discountAmount => mrp - price;
  double get discountPercentage => mrp > 0 ? ((mrp - price) / mrp) * 100 : 0;
  bool get hasDiscount => mrp > price;

  String get formattedDiscountPercentage => '${discountPercentage.toStringAsFixed(0)}% OFF';
  String get formattedDiscountAmount => 'Save ₹${discountAmount.toStringAsFixed(2)}';

  // Price display for user app
  String get priceDisplayText {
    if (hasDiscount) {
      return '₹${price.toStringAsFixed(2)}';
    }
    return '₹${price.toStringAsFixed(2)}';
  }

  String get mrpDisplayText {
    if (hasDiscount) {
      return '₹${mrp.toStringAsFixed(2)}';
    }
    return '';
  }
  
  @override
  String toString() {
    return 'Product(id: $id, name: $name, price: $price, category: $category)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Product && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Product validation class
class ProductValidator {
  static const int maxNameLength = 100;
  static const int maxDescriptionLength = 500;
  static const double minPrice = 0.01;
  static const double maxPrice = 99999.99;

  static String? validateName(String? name) {
    if (name == null || name.trim().isEmpty) {
      return 'Product name is required';
    }
    if (name.trim().length > maxNameLength) {
      return 'Product name must be less than $maxNameLength characters';
    }
    return null;
  }

  static String? validatePrice(String? priceStr) {
    if (priceStr == null || priceStr.trim().isEmpty) {
      return 'Price is required';
    }
    
    final price = double.tryParse(priceStr.trim());
    if (price == null) {
      return 'Please enter a valid price';
    }
    
    if (price < minPrice) {
      return 'Price must be at least ₹$minPrice';
    }
    
    if (price > maxPrice) {
      return 'Price must be less than ₹$maxPrice';
    }
    
    return null;
  }

  static String? validateDescription(String? description) {
    if (description == null || description.trim().isEmpty) {
      return 'Description is required';
    }
    if (description.trim().length > maxDescriptionLength) {
      return 'Description must be less than $maxDescriptionLength characters';
    }
    return null;
  }

  static String? validateCategory(String? category) {
    if (category == null || category.trim().isEmpty) {
      return 'Category is required';
    }
    return null;
  }

  static String? validateMrp(String? mrpStr, {bool isRequired = true}) {
    if (mrpStr == null || mrpStr.trim().isEmpty) {
      return isRequired ? 'MRP is required' : null;
    }

    final mrp = double.tryParse(mrpStr.trim());
    if (mrp == null) {
      return 'Please enter a valid MRP';
    }

    if (mrp < minPrice) {
      return 'MRP must be at least ₹$minPrice';
    }

    if (mrp > maxPrice) {
      return 'MRP must be less than ₹$maxPrice';
    }

    return null;
  }

  static String? validatePriceVsMrp(double price, double mrp) {
    if (price > mrp) {
      return 'Selling price cannot be higher than MRP';
    }

    if (price == mrp) {
      return null; // No discount is fine
    }

    // Calculate discount percentage
    final discountPercentage = ((mrp - price) / mrp) * 100;
    if (discountPercentage > 90) {
      return 'Discount cannot be more than 90%';
    }

    return null;
  }

  static String? validateStockQuantity(String? quantityStr) {
    if (quantityStr == null || quantityStr.trim().isEmpty) {
      return null; // Optional field
    }

    final quantity = int.tryParse(quantityStr.trim());
    if (quantity == null) {
      return 'Please enter a valid quantity';
    }

    if (quantity < 0) {
      return 'Quantity cannot be negative';
    }

    return null;
  }
}

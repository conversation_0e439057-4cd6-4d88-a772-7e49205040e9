import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/payment_config_model.dart';

class PaymentConfigService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = 'payment_config';
  static const String _configDocId = 'main_config';

  // Get payment configuration
  static Future<PaymentConfigModel> getPaymentConfig() async {
    try {
      final doc = await _firestore.collection(_collection).doc(_configDocId).get();
      
      if (doc.exists) {
        return PaymentConfigModel.fromFirestore(doc);
      } else {
        // Create default configuration if it doesn't exist
        final defaultConfig = PaymentConfigModel(
          id: _configDocId,
          createdAt: DateTime.now(),
        );
        await _firestore.collection(_collection).doc(_configDocId).set(defaultConfig.toFirestore());
        return defaultConfig;
      }
    } catch (e) {
      throw Exception('Failed to get payment configuration: $e');
    }
  }

  // Get payment configuration stream
  static Stream<PaymentConfigModel> getPaymentConfigStream() {
    return _firestore
        .collection(_collection)
        .doc(_configDocId)
        .snapshots()
        .map((doc) {
      if (doc.exists) {
        return PaymentConfigModel.fromFirestore(doc);
      } else {
        // Return default config if document doesn't exist
        return PaymentConfigModel(
          id: _configDocId,
          createdAt: DateTime.now(),
        );
      }
    });
  }

  // Update payment configuration
  static Future<void> updatePaymentConfig(PaymentConfigModel config) async {
    try {
      final updatedConfig = config.copyWith(updatedAt: DateTime.now());
      await _firestore
          .collection(_collection)
          .doc(_configDocId)
          .set(updatedConfig.toFirestore(), SetOptions(merge: true));
    } catch (e) {
      throw Exception('Failed to update payment configuration: $e');
    }
  }

  // Update specific PhonePe settings
  static Future<void> updatePhonePeConfig({
    required bool isEnabled,
    required String merchantId,
    required String saltKey,
    required int saltIndex,
    required String baseUrl,
    required bool isTestMode,
  }) async {
    try {
      await _firestore.collection(_collection).doc(_configDocId).update({
        'isPhonePeEnabled': isEnabled,
        'phonePeMerchantId': merchantId,
        'phonePeSaltKey': saltKey,
        'phonePeSaltIndex': saltIndex,
        'phonePeBaseUrl': baseUrl,
        'isPhonePeTestMode': isTestMode,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('Failed to update PhonePe configuration: $e');
    }
  }

  // Update specific Razorpay settings
  static Future<void> updateRazorpayConfig({
    required bool isEnabled,
    required String keyId,
    required String keySecret,
    required bool isTestMode,
  }) async {
    try {
      await _firestore.collection(_collection).doc(_configDocId).update({
        'isRazorpayEnabled': isEnabled,
        'razorpayKeyId': keyId,
        'razorpayKeySecret': keySecret,
        'isRazorpayTestMode': isTestMode,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('Failed to update Razorpay configuration: $e');
    }
  }

  // Update specific UPI settings
  static Future<void> updateUpiConfig({
    required bool isEnabled,
    required String merchantId,
    required String merchantName,
  }) async {
    try {
      await _firestore.collection(_collection).doc(_configDocId).update({
        'isUpiEnabled': isEnabled,
        'upiMerchantId': merchantId,
        'upiMerchantName': merchantName,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('Failed to update UPI configuration: $e');
    }
  }

  // Update COD settings
  static Future<void> updateCodConfig({
    required bool isEnabled,
    required double minAmount,
    required double maxAmount,
    required double charges,
  }) async {
    try {
      await _firestore.collection(_collection).doc(_configDocId).update({
        'isCodEnabled': isEnabled,
        'codMinAmount': minAmount,
        'codMaxAmount': maxAmount,
        'codCharges': charges,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('Failed to update COD configuration: $e');
    }
  }

  // Toggle payment method
  static Future<void> togglePaymentMethod(String method, bool isEnabled) async {
    try {
      final updateData = <String, dynamic>{
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      };

      switch (method.toLowerCase()) {
        case 'phonepe':
          updateData['isPhonePeEnabled'] = isEnabled;
          break;
        case 'razorpay':
          updateData['isRazorpayEnabled'] = isEnabled;
          break;
        case 'paytm':
          updateData['isPaytmEnabled'] = isEnabled;
          break;
        case 'upi':
          updateData['isUpiEnabled'] = isEnabled;
          break;
        case 'cod':
          updateData['isCodEnabled'] = isEnabled;
          break;
        default:
          throw Exception('Unknown payment method: $method');
      }

      await _firestore.collection(_collection).doc(_configDocId).update(updateData);
    } catch (e) {
      throw Exception('Failed to toggle payment method: $e');
    }
  }

  // Set default payment method
  static Future<void> setDefaultPaymentMethod(String method) async {
    try {
      await _firestore.collection(_collection).doc(_configDocId).update({
        'defaultPaymentMethod': method,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('Failed to set default payment method: $e');
    }
  }

  // Enable/disable all payments
  static Future<void> togglePayments(bool isEnabled) async {
    try {
      await _firestore.collection(_collection).doc(_configDocId).update({
        'isPaymentEnabled': isEnabled,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('Failed to toggle payments: $e');
    }
  }

  // Test payment configuration
  static Future<Map<String, dynamic>> testPaymentConfig(String method) async {
    try {
      final config = await getPaymentConfig();
      
      switch (method.toLowerCase()) {
        case 'phonepe':
          if (!config.hasValidPhonePeConfig) {
            return {
              'success': false,
              'error': 'PhonePe configuration is incomplete'
            };
          }
          // Here you would test the actual PhonePe API
          return {
            'success': true,
            'message': 'PhonePe configuration is valid'
          };
          
        case 'razorpay':
          if (!config.hasValidRazorpayConfig) {
            return {
              'success': false,
              'error': 'Razorpay configuration is incomplete'
            };
          }
          return {
            'success': true,
            'message': 'Razorpay configuration is valid'
          };
          
        case 'upi':
          if (!config.isUpiEnabled || config.upiMerchantId.isEmpty) {
            return {
              'success': false,
              'error': 'UPI configuration is incomplete'
            };
          }
          return {
            'success': true,
            'message': 'UPI configuration is valid'
          };
          
        default:
          return {
            'success': false,
            'error': 'Unknown payment method'
          };
      }
    } catch (e) {
      return {
        'success': false,
        'error': 'Test failed: $e'
      };
    }
  }

  // Get payment statistics
  static Future<Map<String, dynamic>> getPaymentStats() async {
    try {
      // This would typically fetch from your orders/transactions collection
      // For now, returning mock data
      return {
        'totalTransactions': 0,
        'successfulTransactions': 0,
        'failedTransactions': 0,
        'totalAmount': 0.0,
        'phonepeTransactions': 0,
        'upiTransactions': 0,
        'codTransactions': 0,
      };
    } catch (e) {
      throw Exception('Failed to get payment statistics: $e');
    }
  }
}

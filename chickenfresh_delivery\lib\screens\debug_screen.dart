import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import '../services/auth_service.dart';
import '../utils/firebase_test.dart';

class DebugScreen extends StatefulWidget {
  const DebugScreen({super.key});

  @override
  State<DebugScreen> createState() => _DebugScreenState();
}

class _DebugScreenState extends State<DebugScreen> {
  final _usernameController = TextEditingController();
  Map<String, dynamic>? _authTestResults;
  Map<String, dynamic>? _firebaseTestResults;
  Map<String, dynamic>? _userTestResults;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _runInitialTests();
  }

  @override
  void dispose() {
    _usernameController.dispose();
    super.dispose();
  }

  Future<void> _runInitialTests() async {
    if (!kDebugMode) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      final authResults = await AuthService().testAuthSetup();
      final firebaseResults = await FirebaseTest.testConfiguration();
      
      setState(() {
        _authTestResults = authResults;
        _firebaseTestResults = firebaseResults;
      });
    } catch (e) {
      print('Error running initial tests: $e');
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Future<void> _testUser() async {
    if (_usernameController.text.trim().isEmpty) return;
    
    setState(() {
      _isLoading = true;
    });

    try {
      final results = await AuthService().testDeliveryPersonExists(_usernameController.text.trim());
      setState(() {
        _userTestResults = results;
      });
    } catch (e) {
      setState(() {
        _userTestResults = {'error': e.toString()};
      });
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  Widget _buildTestResults(String title, Map<String, dynamic>? results) {
    if (results == null) return const SizedBox.shrink();
    
    return Card(
      margin: const EdgeInsets.all(8.0),
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: const TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            ...results.entries.map((entry) => Padding(
              padding: const EdgeInsets.symmetric(vertical: 2.0),
              child: Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  SizedBox(
                    width: 150,
                    child: Text(
                      '${entry.key}:',
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                  ),
                  Expanded(
                    child: Text(
                      entry.value.toString(),
                      style: TextStyle(
                        color: entry.value == true 
                          ? Colors.green 
                          : entry.value == false 
                            ? Colors.red 
                            : null,
                      ),
                    ),
                  ),
                ],
              ),
            )).toList(),
          ],
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Only show debug screen in debug mode
    if (!kDebugMode) {
      return const Scaffold(
        body: Center(
          child: Text('Debug screen only available in debug mode'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Authentication Debug'),
        backgroundColor: Colors.orange,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Platform info
            Card(
              color: Colors.blue.shade50,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Platform: ${kIsWeb ? 'Web' : 'Mobile'}',
                      style: const TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (kIsWeb) ...[
                      Text('URL: ${Uri.base}'),
                      Text('Host: ${Uri.base.host}'),
                      Text('Port: ${Uri.base.port}'),
                    ],
                  ],
                ),
              ),
            ),
            
            // User test section
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Test Delivery Person',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    TextField(
                      controller: _usernameController,
                      decoration: const InputDecoration(
                        labelText: 'Username',
                        hintText: 'Enter delivery person username',
                        border: OutlineInputBorder(),
                      ),
                    ),
                    const SizedBox(height: 8),
                    ElevatedButton(
                      onPressed: _isLoading ? null : _testUser,
                      child: _isLoading 
                        ? const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text('Test User'),
                    ),
                  ],
                ),
              ),
            ),

            // Test results
            if (_isLoading && _authTestResults == null)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32.0),
                  child: CircularProgressIndicator(),
                ),
              ),

            _buildTestResults('Firebase Configuration', _firebaseTestResults),
            _buildTestResults('Auth Service Status', _authTestResults),
            _buildTestResults('User Test Results', _userTestResults),

            // Refresh button
            Center(
              child: ElevatedButton(
                onPressed: _runInitialTests,
                child: const Text('Refresh Tests'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

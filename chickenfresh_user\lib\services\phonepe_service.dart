import 'dart:convert';
import 'dart:math';
import 'package:crypto/crypto.dart';
import 'package:http/http.dart' as http;
import 'package:url_launcher/url_launcher.dart';
import '../models/payment_config_model.dart';
import 'payment_config_service.dart';

class PhonePeService {
  // Get payment configuration
  static Future<PaymentConfigModel> _getConfig() async {
    return await PaymentConfigService.getPaymentConfig();
  }

  // Generate transaction ID
  static String _generateTransactionId() {
    final random = Random();
    final timestamp = DateTime.now().millisecondsSinceEpoch;
    final randomNum = random.nextInt(999999);
    return 'TXN_${timestamp}_$randomNum';
  }

  // Generate checksum for PhonePe API
  static String _generateChecksum(String payload, String saltKey, int saltIndex) {
    final data = payload + '/pg/v1/pay' + saltKey;
    final bytes = utf8.encode(data);
    final digest = sha256.convert(bytes);
    return '${digest.toString()}###$saltIndex';
  }

  // Create payment request
  static Future<Map<String, dynamic>> createPaymentRequest({
    required double amount,
    required String userId,
    required String userPhone,
    required String userName,
    String? orderId,
  }) async {
    try {
      final config = await _getConfig();

      if (!config.hasValidPhonePeConfig) {
        return {
          'success': false,
          'error': 'PhonePe configuration is not valid',
        };
      }

      final transactionId = orderId ?? _generateTransactionId();
      final amountInPaise = (amount * 100).toInt();

      final paymentData = {
        'merchantId': config.phonePeMerchantId,
        'merchantTransactionId': transactionId,
        'merchantUserId': userId,
        'amount': amountInPaise,
        'redirectUrl': 'https://webhook.site/redirect-url', // Replace with your redirect URL
        'redirectMode': 'POST',
        'callbackUrl': 'https://webhook.site/callback-url', // Replace with your callback URL
        'mobileNumber': userPhone,
        'paymentInstrument': {
          'type': 'PAY_PAGE'
        }
      };

      final payload = base64Encode(utf8.encode(jsonEncode(paymentData)));
      final checksum = _generateChecksum(payload, config.phonePeSaltKey, config.phonePeSaltIndex);

      final headers = {
        'Content-Type': 'application/json',
        'X-VERIFY': checksum,
      };

      final requestBody = {
        'request': payload
      };

      final response = await http.post(
        Uri.parse('${config.phonePeBaseUrl}/pg/v1/pay'),
        headers: headers,
        body: jsonEncode(requestBody),
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        return {
          'success': true,
          'data': responseData,
          'transactionId': transactionId,
        };
      } else {
        return {
          'success': false,
          'error': 'Payment request failed: ${response.statusCode}',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': 'Payment request error: $e',
      };
    }
  }

  // Launch PhonePe payment
  static Future<bool> launchPayment(String paymentUrl) async {
    try {
      final uri = Uri.parse(paymentUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(
          uri,
          mode: LaunchMode.externalApplication,
        );
        return true;
      } else {
        return false;
      }
    } catch (e) {
      return false;
    }
  }

  // Check payment status
  static Future<Map<String, dynamic>> checkPaymentStatus(String transactionId) async {
    try {
      final config = await _getConfig();

      if (!config.hasValidPhonePeConfig) {
        return {
          'success': false,
          'error': 'PhonePe configuration is not valid',
        };
      }

      final payload = base64Encode(utf8.encode('/${config.phonePeMerchantId}/$transactionId'));
      final checksum = _generateChecksum(payload, config.phonePeSaltKey, config.phonePeSaltIndex);

      final headers = {
        'Content-Type': 'application/json',
        'X-VERIFY': checksum,
        'X-MERCHANT-ID': config.phonePeMerchantId,
      };

      final response = await http.get(
        Uri.parse('${config.phonePeBaseUrl}/pg/v1/status/${config.phonePeMerchantId}/$transactionId'),
        headers: headers,
      );

      if (response.statusCode == 200) {
        final responseData = jsonDecode(response.body);
        return {
          'success': true,
          'data': responseData,
        };
      } else {
        return {
          'success': false,
          'error': 'Status check failed: ${response.statusCode}',
        };
      }
    } catch (e) {
      return {
        'success': false,
        'error': 'Status check error: $e',
      };
    }
  }

  // Simulate UPI payment (for demo purposes)
  static Future<bool> simulateUPIPayment({
    required double amount,
    required String merchantName,
    required String transactionNote,
  }) async {
    try {
      final config = await _getConfig();

      // Create UPI payment URL using configured merchant ID
      final upiUrl = 'upi://pay?pa=${config.upiMerchantId}&pn=$merchantName&am=$amount&tn=$transactionNote&cu=INR';

      final uri = Uri.parse(upiUrl);
      if (await canLaunchUrl(uri)) {
        await launchUrl(uri);
        return true;
      } else {
        // Fallback to PhonePe web URL
        final phonePeUrl = 'phonepe://pay?pa=${config.upiMerchantId}&pn=$merchantName&am=$amount&tn=$transactionNote';
        final phonePeUri = Uri.parse(phonePeUrl);

        if (await canLaunchUrl(phonePeUri)) {
          await launchUrl(phonePeUri);
          return true;
        }

        return false;
      }
    } catch (e) {
      return false;
    }
  }

  // Generate QR code data for payment
  static Future<String> generateQRCodeData({
    required double amount,
    required String merchantName,
    required String transactionId,
  }) async {
    final config = await _getConfig();
    return 'upi://pay?pa=${config.upiMerchantId}&pn=$merchantName&am=$amount&tn=Payment for Order $transactionId&cu=INR';
  }
}

import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:flutter/foundation.dart';
import '../models/delivery_person.dart';

class AuthService {
  static final AuthService _instance = AuthService._internal();
  factory AuthService() => _instance;
  AuthService._internal();

  final FirebaseAuth _auth = FirebaseAuth.instance;
  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  User? get currentUser => _auth.currentUser;

  // Check if user is authenticated for delivery
  Future<bool> get isAuthenticated async {
    // Check if Firebase user is authenticated
    if (_auth.currentUser == null) {
      return false;
    }

    // Check local storage for delivery-specific authentication
    final prefs = await SharedPreferences.getInstance();
    return prefs.containsKey('delivery_person_id');
  }

  // Check if Firebase user exists (for step 1 authentication)
  bool get isFirebaseAuthenticated {
    return _auth.currentUser != null;
  }

  // Stream of auth state changes
  Stream<User?> get authStateChanges => _auth.authStateChanges();

  // Sign in with username and password (single step authentication)
  Future<DeliveryPerson?> signInWithUsername(String username, String password) async {
    try {
      // Input validation
      if (username.trim().isEmpty) {
        throw Exception('Username cannot be empty');
      }
      if (password.trim().isEmpty) {
        throw Exception('Password cannot be empty');
      }

      // Convert username to Firebase email format
      final firebaseEmail = '${username.trim().toLowerCase()}@chickenfresh.com';

      if (kDebugMode) {
        print('=== Delivery App Authentication Debug ===');
        print('Platform: ${kIsWeb ? 'Web' : 'Mobile'}');
        print('Username: $username');
        print('Firebase Email: $firebaseEmail');
        print('Firebase Project: ${_auth.app.options.projectId}');
        print('Auth Domain: ${_auth.app.options.authDomain}');
        if (kIsWeb) {
          print('Web App ID: ${_auth.app.options.appId}');
          print('Current URL: ${Uri.base}');
        }
        print('========================================');
      }

      // First, authenticate with Firebase using username as email
      final userCredential = await _auth.signInWithEmailAndPassword(
        email: firebaseEmail,
        password: password,
      );

      if (userCredential.user == null) {
        throw Exception('Authentication failed - no user returned');
      }

      if (kDebugMode) {
        print('Firebase authentication successful for user: ${userCredential.user!.uid}');
      }

      // Get delivery person document using Firebase user ID
      final deliveryPersonDoc = await _firestore
          .collection('delivery_persons')
          .doc(userCredential.user!.uid)
          .get();

      if (!deliveryPersonDoc.exists) {
        await _auth.signOut();
        throw Exception('You are not authorized to use this app.');
      }

      final deliveryPerson = DeliveryPerson.fromFirestore(deliveryPersonDoc);

      if (!deliveryPerson.isActive) {
        await _auth.signOut();
        throw Exception('Your account has been deactivated. Please contact admin.');
      }

      // Save login state
      await _saveLoginState(deliveryPerson);
      return deliveryPerson;

    } on FirebaseAuthException catch (e) {
      if (kDebugMode) {
        print('=== Firebase Auth Exception Debug ===');
        print('Error Code: ${e.code}');
        print('Error Message: ${e.message}');
        print('Platform: ${kIsWeb ? 'Web' : 'Mobile'}');
        if (kIsWeb) {
          print('Auth Domain: ${_auth.app.options.authDomain}');
          print('Current Host: ${Uri.base.host}');
          print('Is Localhost: ${Uri.base.host == 'localhost'}');
        }
        print('====================================');
      }

      String errorMessage;
      switch (e.code) {
        case 'user-not-found':
          errorMessage = 'Invalid username or password';
          break;
        case 'wrong-password':
          errorMessage = 'Invalid username or password';
          break;
        case 'invalid-email':
          errorMessage = 'Invalid username format';
          break;
        case 'user-disabled':
          errorMessage = 'Your account has been disabled';
          break;
        case 'too-many-requests':
          errorMessage = 'Too many failed attempts. Please try again later';
          break;
        case 'network-request-failed':
          errorMessage = 'Network error. Please check your internet connection';
          break;
        case 'invalid-credential':
          errorMessage = 'Invalid username or password';
          break;
        case 'auth/operation-not-allowed':
          errorMessage = 'Email/password authentication is not enabled';
          break;
        case 'auth/invalid-api-key':
          errorMessage = 'Invalid Firebase API key configuration';
          break;
        case 'auth/app-not-authorized':
          errorMessage = 'App not authorized for this Firebase project';
          break;
        case 'auth/web-storage-unsupported':
          errorMessage = 'Web storage is not supported or disabled';
          break;
        default:
          errorMessage = kDebugMode
            ? 'Authentication failed: ${e.message ?? e.code}'
            : 'Authentication failed. Please try again.';
      }
      throw Exception(errorMessage);
    } catch (e) {
      if (kDebugMode) {
        print('Sign in error: $e');
      }
      throw Exception('Sign in failed: $e');
    }
  }



  // Get current delivery person details
  Future<DeliveryPerson?> getCurrentDeliveryPerson() async {
    final isAuth = await isAuthenticated;
    if (!isAuth) return null;

    try {
      String? deliveryPersonId;

      // Try to get ID from Firebase Auth first
      if (currentUser != null) {
        deliveryPersonId = currentUser!.uid;
      } else {
        // Get ID from local storage for admin-authenticated users
        final prefs = await SharedPreferences.getInstance();
        deliveryPersonId = prefs.getString('delivery_person_id');
      }

      if (deliveryPersonId == null) return null;

      final doc = await _firestore
          .collection('delivery_persons')
          .doc(deliveryPersonId)
          .get();

      if (doc.exists) {
        return DeliveryPerson.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting delivery person: $e');
      }
      return null;
    }
  }

  // Sign out from delivery authentication only (keeps Firebase auth)
  Future<void> signOutDelivery() async {
    try {
      await _clearLoginState();
    } catch (e) {
      throw Exception('Delivery sign out failed: $e');
    }
  }

  // Sign out completely (both Firebase and delivery)
  Future<void> signOut() async {
    try {
      await _auth.signOut();
      await _clearLoginState();
    } catch (e) {
      throw Exception('Sign out failed: $e');
    }
  }

  // Change delivery person password
  Future<void> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      // Ensure user is authenticated
      if (!await isAuthenticated) {
        throw Exception('You must be logged in to change password');
      }

      // Ensure Firebase user is authenticated
      if (_auth.currentUser == null) {
        throw Exception('Firebase authentication required');
      }

      // Get current delivery person info
      final deliveryPersonInfo = await getSavedDeliveryPersonInfo();
      if (deliveryPersonInfo == null) {
        throw Exception('Delivery person information not found');
      }

      // Get delivery person document
      final deliveryPersonDoc = await _firestore
          .collection('delivery_persons')
          .doc(_auth.currentUser!.uid)
          .get();

      if (!deliveryPersonDoc.exists) {
        throw Exception('Delivery person not found');
      }

      // Verify current password against Firebase Auth
      final deliveryPerson = DeliveryPerson.fromFirestore(deliveryPersonDoc);
      final firebaseEmail = '${deliveryPerson.username}@chickenfresh.com';

      try {
        // Re-authenticate with current password to verify it
        final credential = EmailAuthProvider.credential(
          email: firebaseEmail,
          password: currentPassword,
        );
        await _auth.currentUser!.reauthenticateWithCredential(credential);
      } catch (e) {
        throw Exception('Current password is incorrect');
      }

      // Update Firebase Auth password
      await _auth.currentUser!.updatePassword(newPassword);

      // Update timestamp in Firestore for tracking
      await deliveryPersonDoc.reference.update({
        'passwordUpdatedByDelivery': true,
        'passwordUpdatedAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      if (kDebugMode) {
        print('Password changed successfully for delivery person: ${deliveryPersonInfo['id']}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Change password error: $e');
      }
      throw Exception('Failed to change password: $e');
    }
  }

  // Save login state to local storage
  Future<void> _saveLoginState(DeliveryPerson deliveryPerson) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString('delivery_person_id', deliveryPerson.id);
    await prefs.setString('delivery_person_name', deliveryPerson.name);
    await prefs.setString('delivery_person_email', deliveryPerson.email);
  }

  // Clear login state from local storage
  Future<void> _clearLoginState() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove('delivery_person_id');
    await prefs.remove('delivery_person_name');
    await prefs.remove('delivery_person_email');
  }

  // Get saved delivery person info
  Future<Map<String, String>?> getSavedDeliveryPersonInfo() async {
    final prefs = await SharedPreferences.getInstance();
    final id = prefs.getString('delivery_person_id');
    final name = prefs.getString('delivery_person_name');
    final email = prefs.getString('delivery_person_email');

    if (id != null && name != null && email != null) {
      return {
        'id': id,
        'name': name,
        'email': email,
      };
    }
    return null;
  }

  // Test authentication setup for debugging
  Future<Map<String, dynamic>> testAuthSetup() async {
    final results = <String, dynamic>{};

    try {
      // Basic Firebase setup
      results['firebase_initialized'] = _auth.app.name == '[DEFAULT]';
      results['project_id'] = _auth.app.options.projectId;
      results['auth_domain'] = _auth.app.options.authDomain;
      results['api_key'] = _auth.app.options.apiKey.substring(0, 10) + '...';

      // Platform-specific info
      results['platform'] = kIsWeb ? 'web' : 'mobile';
      if (kIsWeb) {
        results['web_app_id'] = _auth.app.options.appId;
        results['current_url'] = Uri.base.toString();
        results['is_localhost'] = Uri.base.host == 'localhost';
      }

      // Test Firestore connectivity
      try {
        await _firestore.collection('delivery_persons').limit(1).get();
        results['firestore_connected'] = true;
      } catch (e) {
        results['firestore_connected'] = false;
        results['firestore_error'] = e.toString();
      }

      // Test Auth service
      results['current_user'] = _auth.currentUser?.uid ?? 'none';
      results['auth_state_available'] = true;

      // Check local storage
      final prefs = await SharedPreferences.getInstance();
      results['has_saved_delivery_id'] = prefs.containsKey('delivery_person_id');

    } catch (e) {
      results['test_error'] = e.toString();
    }

    return results;
  }

  // Test specific delivery person existence (for debugging)
  Future<Map<String, dynamic>> testDeliveryPersonExists(String username) async {
    final results = <String, dynamic>{};

    try {
      final firebaseEmail = '${username.trim().toLowerCase()}@chickenfresh.com';
      results['firebase_email'] = firebaseEmail;

      // Check if user exists in Firebase Auth
      try {
        final methods = await _auth.fetchSignInMethodsForEmail(firebaseEmail);
        results['user_exists_in_auth'] = methods.isNotEmpty;
        results['sign_in_methods'] = methods;
      } catch (e) {
        results['user_exists_in_auth'] = false;
        results['auth_check_error'] = e.toString();
      }

      // Check if delivery person document exists
      try {
        final query = await _firestore
            .collection('delivery_persons')
            .where('username', isEqualTo: username.trim().toLowerCase())
            .limit(1)
            .get();

        results['delivery_doc_exists'] = query.docs.isNotEmpty;
        if (query.docs.isNotEmpty) {
          final doc = query.docs.first;
          results['delivery_doc_id'] = doc.id;
          final data = doc.data();
          results['is_active'] = data['isActive'] ?? false;
          results['name'] = data['name'] ?? 'Unknown';
        }
      } catch (e) {
        results['delivery_doc_exists'] = false;
        results['firestore_check_error'] = e.toString();
      }

    } catch (e) {
      results['test_error'] = e.toString();
    }

    return results;
  }

  // Print authentication debug info (debug mode only)
  Future<void> printAuthDebugInfo() async {
    if (!kDebugMode) return;

    print('=== Auth Service Debug Info ===');
    final results = await testAuthSetup();
    results.forEach((key, value) {
      print('$key: $value');
    });
    print('==============================');
  }
}

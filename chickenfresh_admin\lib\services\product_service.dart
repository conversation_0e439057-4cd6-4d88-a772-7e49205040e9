import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:flutter/foundation.dart';
import '../models/product.dart';
import 'auth_service.dart';
import 'storage_service.dart';
import 'dart:io';

class ProductService {
  static final ProductService _instance = ProductService._internal();
  factory ProductService() => _instance;
  ProductService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AuthService _authService = AuthService();
  final StorageService _storageService = StorageService();

  // Get all products
  Stream<List<Product>> getProducts() {
    return _firestore
        .collection('products')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        try {
          return Product.fromFirestore(doc);
        } catch (e) {
          if (kDebugMode) {
            print('Error parsing product ${doc.id}: $e');
          }
          // Return a placeholder product or skip
          return null;
        }
      }).where((product) => product != null).cast<Product>().toList();
    });
  }

  // Get products by category
  Stream<List<Product>> getProductsByCategory(String category) {
    return _firestore
        .collection('products')
        .where('category', isEqualTo: category)
        .where('isAvailable', isEqualTo: true)
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        try {
          return Product.fromFirestore(doc);
        } catch (e) {
          if (kDebugMode) {
            print('Error parsing product ${doc.id}: $e');
          }
          return null;
        }
      }).where((product) => product != null).cast<Product>().toList();
    });
  }

  // Get all products as list
  Future<List<Product>> getAllProducts() async {
    try {
      final snapshot = await _firestore
          .collection('products')
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) {
        try {
          return Product.fromFirestore(doc);
        } catch (e) {
          if (kDebugMode) {
            print('Error parsing product ${doc.id}: $e');
          }
          return null;
        }
      }).where((product) => product != null).cast<Product>().toList();
    } catch (e) {
      throw ProductException('Failed to get all products: $e');
    }
  }

  // Get single product
  Future<Product?> getProduct(String productId) async {
    try {
      final doc = await _firestore
          .collection('products')
          .doc(productId)
          .get();

      if (!doc.exists) return null;

      return Product.fromFirestore(doc);
    } catch (e) {
      throw ProductException('Failed to get product: $e');
    }
  }

  // Add new product
  Future<String> addProduct({
    required String name,
    required double price,
    required double mrp,
    required String description,
    required String category,
    File? imageFile,
    int stockQuantity = 0,
    bool isAvailable = true,
  }) async {
    try {
      // Check authentication
      if (!_authService.isAuthenticated) {
        throw ProductException('Authentication required');
      }

      final user = _authService.currentUser!;
      
      // Validate input
      ProductValidator.validateName(name);
      ProductValidator.validatePrice(price.toString());
      ProductValidator.validateMrp(mrp.toString());
      ProductValidator.validatePriceVsMrp(price, mrp);
      ProductValidator.validateDescription(description);
      ProductValidator.validateCategory(category);

      String? imageUrl;
      
      // Upload image if provided
      if (imageFile != null) {
        imageUrl = await _storageService.uploadProductImage(imageFile);
      }

      final now = DateTime.now();
      final product = Product(
        id: '', // Will be set by Firestore
        name: name.trim(),
        price: price,
        mrp: mrp,
        description: description.trim(),
        category: category.trim(),
        imageUrl: imageUrl,
        stockQuantity: stockQuantity,
        isAvailable: isAvailable,
        createdAt: now,
        updatedAt: now,
        createdBy: user.uid,
      );

      final docRef = await _firestore
          .collection('products')
          .add(product.toFirestore());

      return docRef.id;
    } catch (e) {
      if (e is ProductException) rethrow;
      throw ProductException('Failed to add product: $e');
    }
  }

  // Update product
  Future<void> updateProduct({
    required String productId,
    String? name,
    double? price,
    double? mrp,
    String? description,
    String? category,
    File? imageFile,
    int? stockQuantity,
    bool? isAvailable,
  }) async {
    try {
      // Check authentication
      if (!_authService.isAuthenticated) {
        throw ProductException('Authentication required');
      }

      // Get existing product
      final existingProduct = await getProduct(productId);
      if (existingProduct == null) {
        throw ProductException('Product not found');
      }

      // Validate input if provided
      if (name != null) ProductValidator.validateName(name);
      if (price != null) ProductValidator.validatePrice(price.toString());
      if (mrp != null) ProductValidator.validateMrp(mrp.toString());
      if (price != null && mrp != null) ProductValidator.validatePriceVsMrp(price, mrp);
      if (price != null && mrp == null) ProductValidator.validatePriceVsMrp(price, existingProduct.mrp);
      if (price == null && mrp != null) ProductValidator.validatePriceVsMrp(existingProduct.price, mrp);
      if (description != null) ProductValidator.validateDescription(description);
      if (category != null) ProductValidator.validateCategory(category);

      final updateData = <String, dynamic>{
        'updatedAt': FieldValue.serverTimestamp(),
      };

      // Add fields to update
      if (name != null) updateData['name'] = name.trim();
      if (price != null) updateData['price'] = price;
      if (mrp != null) updateData['mrp'] = mrp;
      if (description != null) updateData['description'] = description.trim();
      if (category != null) updateData['category'] = category.trim();
      if (stockQuantity != null) updateData['stockQuantity'] = stockQuantity;
      if (isAvailable != null) updateData['isAvailable'] = isAvailable;

      // Handle image upload
      if (imageFile != null) {
        // Delete old image if exists
        if (existingProduct.imageUrl != null) {
          await _storageService.deleteImage(existingProduct.imageUrl!);
        }
        
        // Upload new image
        final imageUrl = await _storageService.uploadProductImage(imageFile);
        updateData['imageUrl'] = imageUrl;
      }

      await _firestore
          .collection('products')
          .doc(productId)
          .update(updateData);
    } catch (e) {
      if (e is ProductException) rethrow;
      throw ProductException('Failed to update product: $e');
    }
  }

  // Delete product
  Future<void> deleteProduct(String productId) async {
    try {
      // Check authentication
      if (!_authService.isAuthenticated) {
        throw ProductException('Authentication required');
      }

      // Get product to delete image
      final product = await getProduct(productId);
      if (product == null) {
        throw ProductException('Product not found');
      }

      // Delete image if exists
      if (product.imageUrl != null) {
        await _storageService.deleteImage(product.imageUrl!);
      }

      // Delete product document
      await _firestore
          .collection('products')
          .doc(productId)
          .delete();
    } catch (e) {
      if (e is ProductException) rethrow;
      throw ProductException('Failed to delete product: $e');
    }
  }

  // Update stock quantity
  Future<void> updateStock(String productId, int newQuantity) async {
    try {
      if (!_authService.isAuthenticated) {
        throw ProductException('Authentication required');
      }

      if (newQuantity < 0) {
        throw ProductException('Stock quantity cannot be negative');
      }

      await _firestore
          .collection('products')
          .doc(productId)
          .update({
        'stockQuantity': newQuantity,
        'isAvailable': newQuantity > 0,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      if (e is ProductException) rethrow;
      throw ProductException('Failed to update stock: $e');
    }
  }

  // Toggle product availability
  Future<void> toggleAvailability(String productId) async {
    try {
      if (!_authService.isAuthenticated) {
        throw ProductException('Authentication required');
      }

      final product = await getProduct(productId);
      if (product == null) {
        throw ProductException('Product not found');
      }

      await _firestore
          .collection('products')
          .doc(productId)
          .update({
        'isAvailable': !product.isAvailable,
        'updatedAt': FieldValue.serverTimestamp(),
      });
    } catch (e) {
      if (e is ProductException) rethrow;
      throw ProductException('Failed to toggle availability: $e');
    }
  }

  // Search products
  Future<List<Product>> searchProducts(String query) async {
    try {
      if (query.trim().isEmpty) return [];

      final queryLower = query.toLowerCase().trim();
      
      // Search by name (Firestore doesn't support full-text search)
      final nameQuery = await _firestore
          .collection('products')
          .where('name', isGreaterThanOrEqualTo: queryLower)
          .where('name', isLessThanOrEqualTo: '$queryLower\uf8ff')
          .get();

      // Search by category
      final categoryQuery = await _firestore
          .collection('products')
          .where('category', isGreaterThanOrEqualTo: queryLower)
          .where('category', isLessThanOrEqualTo: '$queryLower\uf8ff')
          .get();

      final products = <Product>[];
      final seenIds = <String>{};

      // Add products from name search
      for (final doc in nameQuery.docs) {
        if (!seenIds.contains(doc.id)) {
          try {
            products.add(Product.fromFirestore(doc));
            seenIds.add(doc.id);
          } catch (e) {
            if (kDebugMode) {
              print('Error parsing product ${doc.id}: $e');
            }
          }
        }
      }

      // Add products from category search
      for (final doc in categoryQuery.docs) {
        if (!seenIds.contains(doc.id)) {
          try {
            products.add(Product.fromFirestore(doc));
            seenIds.add(doc.id);
          } catch (e) {
            if (kDebugMode) {
              print('Error parsing product ${doc.id}: $e');
            }
          }
        }
      }

      return products;
    } catch (e) {
      throw ProductException('Failed to search products: $e');
    }
  }

  // Get product categories
  Future<List<String>> getCategories() async {
    try {
      final snapshot = await _firestore
          .collection('products')
          .get();

      final categories = <String>{};
      for (final doc in snapshot.docs) {
        final data = doc.data();
        final category = data['category'] as String?;
        if (category != null && category.isNotEmpty) {
          categories.add(category);
        }
      }

      final categoryList = categories.toList();
      categoryList.sort();
      return categoryList;
    } catch (e) {
      throw ProductException('Failed to get categories: $e');
    }
  }

  // Get low stock products
  Stream<List<Product>> getLowStockProducts({int threshold = 5}) {
    return _firestore
        .collection('products')
        .where('stockQuantity', isLessThanOrEqualTo: threshold)
        .where('stockQuantity', isGreaterThan: 0)
        .snapshots()
        .map((snapshot) {
      return snapshot.docs.map((doc) {
        try {
          return Product.fromFirestore(doc);
        } catch (e) {
          if (kDebugMode) {
            print('Error parsing product ${doc.id}: $e');
          }
          return null;
        }
      }).where((product) => product != null).cast<Product>().toList();
    });
  }
}

// Custom exception class
class ProductException implements Exception {
  final String message;
  
  ProductException(this.message);
  
  @override
  String toString() => message;
}

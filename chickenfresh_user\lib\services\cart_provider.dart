
import 'package:flutter/material.dart';
import '../models/product_model.dart';

class CartProvider with ChangeNotifier {
  final List<Product> _products = [];

  List<Product> get products => _products;

  double get totalPrice => _products.fold(0.0, (sum, item) => sum + (item.price * item.quantity));

  void addProduct(Product product) {
    int index = _products.indexWhere((p) => p.id == product.id);
    if (index != -1) {
      _products[index].quantity++;
    } else {
      // Start with quantity 1 when first added to cart
      product.quantity = 1;
      _products.add(product);
    }
    notifyListeners();
  }

  void removeProduct(Product product) {
    int index = _products.indexWhere((p) => p.id == product.id);
    if (index != -1) {
      if (_products[index].quantity > 1) {
        _products[index].quantity--;
      } else {
        // Set quantity to 0 instead of removing from list
        _products[index].quantity = 0;
      }
    }
    notifyListeners();
  }

  void clearCart() {
    _products.clear();
    notifyListeners();
  }

  // Add product to list without changing quantity (for initial loading)
  void addProductToList(Product product) {
    int index = _products.indexWhere((p) => p.id == product.id);
    if (index == -1) {
      _products.add(product);
      notifyListeners();
    }
  }

  // Get total number of items in cart
  int get totalItems => _products.fold(0, (total, item) => total + item.quantity);

  // Get number of unique products in cart
  int get uniqueItemsCount => _products.where((p) => p.quantity > 0).length;

  // Check if cart is empty
  bool get isEmpty => _products.every((p) => p.quantity == 0);

  // Check if cart has items
  bool get isNotEmpty => _products.any((p) => p.quantity > 0);

  // Get cart items (products with quantity > 0)
  List<Product> get cartItems => _products.where((p) => p.quantity > 0).toList();

  // Remove specific product completely from cart
  void removeProductCompletely(Product product) {
    int index = _products.indexWhere((p) => p.id == product.id);
    if (index != -1) {
      _products[index].quantity = 0;
    }
    notifyListeners();
  }

  // Update product quantity directly
  void updateProductQuantity(Product product, int quantity) {
    int index = _products.indexWhere((p) => p.id == product.id);
    if (index != -1) {
      _products[index].quantity = quantity.clamp(0, 999); // Max 999 items
    }
    notifyListeners();
  }

  // Get quantity of specific product
  int getProductQuantity(String productId) {
    int index = _products.indexWhere((p) => p.id == productId);
    return index != -1 ? _products[index].quantity : 0;
  }

  // Check if product is in cart
  bool isProductInCart(String productId) {
    return _products.any((p) => p.id == productId && p.quantity > 0);
  }
}

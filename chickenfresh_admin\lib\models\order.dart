import 'package:cloud_firestore/cloud_firestore.dart';

enum OrderStatus {
  pending,
  confirmed,
  processing,
  outForDelivery,
  delivered,
  cancelled,
  partialCancelled,
  refunded
}

enum PaymentMethod {
  cash,
  upi,
  card
}

class OrderItem {
  final String productId;
  final String productName;
  final double price;
  final int quantity;
  final double totalPrice;

  OrderItem({
    required this.productId,
    required this.productName,
    required this.price,
    required this.quantity,
    required this.totalPrice,
  });

  factory OrderItem.fromMap(Map<String, dynamic> map) {
    return OrderItem(
      productId: map['productId'] ?? '',
      productName: map['productName'] ?? '',
      price: (map['price'] ?? 0).toDouble(),
      quantity: map['quantity'] ?? 0,
      totalPrice: (map['totalPrice'] ?? 0).toDouble(),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'productId': productId,
      'productName': productName,
      'price': price,
      'quantity': quantity,
      'totalPrice': totalPrice,
    };
  }
}

class DeliveryAddress {
  final String apartmentName;
  final String blockName;
  final String houseNumber;
  final String? landmark;
  final String? specialInstructions;

  DeliveryAddress({
    required this.apartmentName,
    required this.blockName,
    required this.houseNumber,
    this.landmark,
    this.specialInstructions,
  });

  factory DeliveryAddress.fromMap(Map<String, dynamic> map) {
    return DeliveryAddress(
      apartmentName: map['apartmentName'] ?? '',
      blockName: map['blockName'] ?? '',
      houseNumber: map['houseNumber'] ?? '',
      landmark: map['landmark'],
      specialInstructions: map['specialInstructions'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'apartmentName': apartmentName,
      'blockName': blockName,
      'houseNumber': houseNumber,
      'landmark': landmark,
      'specialInstructions': specialInstructions,
    };
  }

  String get fullAddress {
    final parts = [houseNumber, blockName, apartmentName];
    if (landmark != null && landmark!.isNotEmpty) {
      parts.add('Near $landmark');
    }
    return parts.join(', ');
  }
}

class CustomerInfo {
  final String name;
  final String phoneNumber;
  final String? email;

  CustomerInfo({
    required this.name,
    required this.phoneNumber,
    this.email,
  });

  factory CustomerInfo.fromMap(Map<String, dynamic> map) {
    return CustomerInfo(
      name: map['name'] ?? '',
      phoneNumber: map['phoneNumber'] ?? '',
      email: map['email'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'phoneNumber': phoneNumber,
      'email': email,
    };
  }
}

class Order {
  final String id;
  final String orderNumber;
  final List<OrderItem> items;
  final double subtotal;
  final double deliveryFee;
  final double totalAmount;
  final OrderStatus status;
  final CustomerInfo customerInfo;
  final DeliveryAddress deliveryAddress;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deliveryDate;
  final String? notes;
  final String? cancellationReason;
  final String? assignedDeliveryPersonId;
  final String? assignedDeliveryPersonName;
  final PaymentMethod? paymentMethod;
  final bool? paymentCollected;

  Order({
    required this.id,
    required this.orderNumber,
    required this.items,
    required this.subtotal,
    this.deliveryFee = 0.0,
    required this.totalAmount,
    this.status = OrderStatus.pending,
    required this.customerInfo,
    required this.deliveryAddress,
    required this.createdAt,
    required this.updatedAt,
    this.deliveryDate,
    this.notes,
    this.cancellationReason,
    this.assignedDeliveryPersonId,
    this.assignedDeliveryPersonName,
    this.paymentMethod,
    this.paymentCollected,
  });

  factory Order.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    // Check if this is the new admin format or legacy user format
    bool isLegacyFormat = data.containsKey('userName') || data.containsKey('userPhone');

    if (isLegacyFormat) {
      // Parse legacy user app format
      return _fromLegacyFormat(doc.id, data);
    } else {
      // Parse new admin format
      return Order(
        id: doc.id,
        orderNumber: data['orderNumber'] ?? '',
        items: (data['items'] as List<dynamic>?)
            ?.map((item) => OrderItem.fromMap(item as Map<String, dynamic>))
            .toList() ?? [],
        subtotal: (data['subtotal'] ?? 0).toDouble(),
        deliveryFee: (data['deliveryFee'] ?? 0).toDouble(),
        totalAmount: (data['totalAmount'] ?? 0).toDouble(),
        status: _parseOrderStatus(data['status']),
        customerInfo: CustomerInfo.fromMap(data['customerInfo'] ?? {}),
        deliveryAddress: DeliveryAddress.fromMap(data['deliveryAddress'] ?? {}),
        createdAt: _parseTimestamp(data['createdAt']),
        updatedAt: _parseTimestamp(data['updatedAt']),
        deliveryDate: data['deliveryDate'] != null
            ? _parseTimestamp(data['deliveryDate'])
            : null,
        notes: data['notes'],
        cancellationReason: data['cancellationReason'],
        assignedDeliveryPersonId: data['assignedDeliveryPersonId'],
        assignedDeliveryPersonName: data['assignedDeliveryPersonName'],
        paymentMethod: _parsePaymentMethod(data['paymentMethod']),
        paymentCollected: data['paymentCollected'],
      );
    }
  }

  // Parse legacy user app order format
  static Order _fromLegacyFormat(String docId, Map<String, dynamic> data) {
    // Parse products from legacy format
    List<OrderItem> items = [];
    if (data['products'] != null) {
      final products = data['products'] as List<dynamic>;
      items = products.map((product) {
        final productMap = product as Map<String, dynamic>;
        return OrderItem(
          productId: '', // Legacy format doesn't have productId
          productName: productMap['name'] ?? '',
          price: (productMap['price'] ?? 0).toDouble(),
          quantity: productMap['quantity'] ?? 0,
          totalPrice: (productMap['price'] ?? 0).toDouble() * (productMap['quantity'] ?? 0),
        );
      }).toList();
    }

    // Calculate totals
    double totalAmount = (data['totalPrice'] ?? 0).toDouble();
    double subtotal = totalAmount; // No separate subtotal in legacy format

    // Parse timestamp
    DateTime createdAt = DateTime.now();
    if (data['timestamp'] != null) {
      if (data['timestamp'] is Timestamp) {
        createdAt = (data['timestamp'] as Timestamp).toDate();
      }
    }

    // Generate order number from document ID
    String orderNumber = docId.substring(0, 8).toUpperCase();

    return Order(
      id: docId,
      orderNumber: orderNumber,
      items: items,
      subtotal: subtotal,
      deliveryFee: 0.0, // No delivery fee in legacy format
      totalAmount: totalAmount,
      status: _parseOrderStatus(data['status']), // Parse status from data, default to pending if not found
      customerInfo: CustomerInfo(
        name: data['userName'] ?? '',
        phoneNumber: data['userPhone'] ?? '',
      ),
      deliveryAddress: DeliveryAddress(
        apartmentName: data['apartmentName'] ?? '',
        blockName: data['blockName'] ?? '',
        houseNumber: data['houseNumber'] ?? '',
      ),
      createdAt: createdAt,
      updatedAt: data['updatedAt'] != null ? _parseTimestamp(data['updatedAt']) : createdAt, // Use updatedAt if available
    );
  }

  Map<String, dynamic> toFirestore() {
    return {
      'orderNumber': orderNumber,
      'items': items.map((item) => item.toMap()).toList(),
      'subtotal': subtotal,
      'deliveryFee': deliveryFee,
      'totalAmount': totalAmount,
      'status': status.name,
      'customerInfo': customerInfo.toMap(),
      'deliveryAddress': deliveryAddress.toMap(),
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'deliveryDate': deliveryDate != null 
          ? Timestamp.fromDate(deliveryDate!) 
          : null,
      'notes': notes,
      'cancellationReason': cancellationReason,
    };
  }

  static OrderStatus _parseOrderStatus(dynamic status) {
    if (status is String) {
      try {
        return OrderStatus.values.firstWhere(
          (e) => e.name == status,
          orElse: () => OrderStatus.pending,
        );
      } catch (e) {
        return OrderStatus.pending;
      }
    }
    return OrderStatus.pending;
  }

  static DateTime _parseTimestamp(dynamic timestamp) {
    if (timestamp is Timestamp) {
      return timestamp.toDate();
    }
    if (timestamp is DateTime) {
      return timestamp;
    }
    if (timestamp is String) {
      try {
        return DateTime.parse(timestamp);
      } catch (e) {
        return DateTime.now();
      }
    }
    return DateTime.now();
  }

  static PaymentMethod? _parsePaymentMethod(dynamic method) {
    if (method is String) {
      try {
        return PaymentMethod.values.firstWhere(
          (e) => e.name == method,
        );
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  // Business logic methods
  bool get canBeCancelled =>
      status != OrderStatus.delivered &&
      status != OrderStatus.cancelled &&
      status != OrderStatus.refunded;
  
  bool get isCompleted => 
      status == OrderStatus.delivered || 
      status == OrderStatus.cancelled || 
      status == OrderStatus.refunded;
  
  bool get isActive => !isCompleted;

  bool get canBePartiallyCancelled =>
      canBeCancelled && items.length > 1;

  bool get canBeAssignedForDelivery =>
      status == OrderStatus.confirmed || status == OrderStatus.processing;

  bool get isAssignedForDelivery => assignedDeliveryPersonId != null;
  
  String get statusDisplayName {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.processing:
        return 'Processing';
      case OrderStatus.outForDelivery:
        return 'Out for Delivery';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.partialCancelled:
        return 'Partial Cancelled';
      case OrderStatus.refunded:
        return 'Refunded';
    }
  }

  String get formattedTotalAmount => '₹${totalAmount.toStringAsFixed(2)}';
  String get formattedSubtotal => '₹${subtotal.toStringAsFixed(2)}';
  String get formattedDeliveryFee => '₹${deliveryFee.toStringAsFixed(2)}';

  int get totalItems => items.fold(0, (total, item) => total + item.quantity);

  Order copyWith({
    String? id,
    String? orderNumber,
    List<OrderItem>? items,
    double? subtotal,
    double? deliveryFee,
    double? totalAmount,
    OrderStatus? status,
    CustomerInfo? customerInfo,
    DeliveryAddress? deliveryAddress,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deliveryDate,
    String? notes,
    String? cancellationReason,
    String? assignedDeliveryPersonId,
    String? assignedDeliveryPersonName,
    PaymentMethod? paymentMethod,
    bool? paymentCollected,
  }) {
    return Order(
      id: id ?? this.id,
      orderNumber: orderNumber ?? this.orderNumber,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      deliveryFee: deliveryFee ?? this.deliveryFee,
      totalAmount: totalAmount ?? this.totalAmount,
      status: status ?? this.status,
      customerInfo: customerInfo ?? this.customerInfo,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      notes: notes ?? this.notes,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      assignedDeliveryPersonId: assignedDeliveryPersonId ?? this.assignedDeliveryPersonId,
      assignedDeliveryPersonName: assignedDeliveryPersonName ?? this.assignedDeliveryPersonName,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentCollected: paymentCollected ?? this.paymentCollected,
    );
  }

  @override
  String toString() {
    return 'Order(id: $id, orderNumber: $orderNumber, status: $status, totalAmount: $totalAmount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is Order && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

// Order number generator
class OrderNumberGenerator {
  static String generate() {
    final now = DateTime.now();
    final timestamp = now.millisecondsSinceEpoch.toString().substring(8);
    final random = (now.microsecond % 1000).toString().padLeft(3, '0');
    return 'NF$timestamp$random';
  }
}

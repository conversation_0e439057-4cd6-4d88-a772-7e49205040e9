@echo off
echo Deploying Firestore Security Rules...

REM Check if Firebase CLI is installed
firebase --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Firebase CLI is not installed. Please install it first:
    echo npm install -g firebase-tools
    pause
    exit /b 1
)

REM Set the project
firebase use chickenfresh-e5b0f

REM Deploy Firestore rules
echo Deploying Firestore rules...
firebase deploy --only firestore:rules

REM Deploy Storage rules
echo Deploying Storage rules...
firebase deploy --only storage:rules

echo.
echo Security rules deployed successfully!
echo.
pause

# Firebase Production Setup Guide

## Prerequisites
1. Install Firebase CLI: `npm install -g firebase-tools`
2. Login to Firebase: `firebase login`

## Step 1: Initialize Firebase Project

```bash
# Navigate to your project directory
cd chickenfresh_admin

# Initialize Firebase (if not already done)
firebase init

# Select the following services:
# - Firestore
# - Storage
# - Hosting (optional)
```

## Step 2: Configure Firebase Project

1. **Select your existing project**: `chickenfresh-e5b0f`
2. **Use existing Firestore rules**: `firestore.rules`
3. **Use existing Storage rules**: `storage.rules`

## Step 3: Add Web App to Firebase Project

1. Go to [Firebase Console](https://console.firebase.google.com/)
2. Select your project: `chickenfresh-e5b0f`
3. Click "Add app" → Web app (</>) 
4. App nickname: `chickenfresh Admin Web`
5. Enable Firebase Hosting: Yes
6. Copy the config object

## Step 4: Update Firebase Configuration

Replace the placeholder in `lib/firebase_options.dart`:

```dart
static const FirebaseOptions web = FirebaseOptions(
  apiKey: 'YOUR_ACTUAL_API_KEY',
  appId: 'YOUR_ACTUAL_WEB_APP_ID', // Replace YOUR_WEB_APP_ID
  messagingSenderId: '841436133900',
  projectId: 'chickenfresh-e5b0f',
  authDomain: 'chickenfresh-e5b0f.firebaseapp.com',
  storageBucket: 'chickenfresh-e5b0f.firebasestorage.app',
  measurementId: 'YOUR_MEASUREMENT_ID', // Replace if using Analytics
);
```

## Step 5: Deploy Security Rules

```bash
# Deploy Firestore rules
firebase deploy --only firestore:rules

# Deploy Storage rules
firebase deploy --only storage:rules
```

## Step 6: Set Up Authentication

1. Go to Firebase Console → Authentication
2. Enable Sign-in methods:
   - Email/Password ✅
   - Google (optional)
3. Add authorized domains for web:
   - `localhost` (for development)
   - Your production domain

## Step 7: Create Admin User

### Option A: Using Firebase Console
1. Go to Authentication → Users
2. Add user manually
3. Set custom claims via Firebase CLI:

```bash
firebase functions:shell
# In the shell:
admin.auth().setCustomUserClaims('USER_UID', {role: 'admin'})
```

### Option B: Using Cloud Functions (Recommended)
Create a Cloud Function to set admin role:

```javascript
const functions = require('firebase-functions');
const admin = require('firebase-admin');

exports.setAdminRole = functions.https.onCall(async (data, context) => {
  // Only allow existing admins to create new admins
  if (!context.auth || context.auth.token.role !== 'admin') {
    throw new functions.https.HttpsError('permission-denied', 'Only admins can set admin roles');
  }
  
  await admin.auth().setCustomUserClaims(data.uid, { role: 'admin' });
  return { success: true };
});
```

## Step 8: Initialize Database Collections

Create initial collections in Firestore:

### Products Collection
```javascript
// Example product document
{
  name: "Fresh Apples",
  price: 120,
  description: "Fresh red apples from local farms",
  category: "Fruits",
  imageUrl: "",
  isAvailable: true,
  stockQuantity: 50,
  createdAt: serverTimestamp(),
  updatedAt: serverTimestamp(),
  createdBy: "admin_user_id"
}
```

### Categories Collection
```javascript
// Example category document
{
  name: "Fruits",
  description: "Fresh fruits and seasonal produce",
  imageUrl: "",
  isActive: true,
  sortOrder: 1
}
```

### App Settings Collection
```javascript
// Example app settings document
{
  deliveryFee: 25,
  minimumOrderAmount: 200,
  maxDeliveryDistance: 10,
  operatingHours: {
    start: "08:00",
    end: "20:00"
  },
  isDeliveryActive: true
}
```

## Step 9: Test the Setup

1. Run the admin app: `flutter run -d chrome`
2. Try to login with admin credentials
3. Test product creation/editing
4. Verify security rules are working

## Step 10: Environment Variables (Production)

For production deployment, use environment variables:

```dart
// lib/config/environment.dart
class Environment {
  static const String firebaseApiKey = String.fromEnvironment('FIREBASE_API_KEY');
  static const String firebaseAppId = String.fromEnvironment('FIREBASE_APP_ID');
  static const String firebaseProjectId = String.fromEnvironment('FIREBASE_PROJECT_ID');
  
  static bool get isProduction => const String.fromEnvironment('ENVIRONMENT') == 'production';
}
```

## Security Checklist

- ✅ Firestore security rules deployed
- ✅ Storage security rules deployed  
- ✅ Admin authentication configured
- ✅ Custom claims for admin role
- ✅ Input validation in security rules
- ✅ Rate limiting enabled
- ✅ CORS configured for web domain
- ✅ API keys restricted to specific domains

## Monitoring & Analytics

1. Enable Firebase Analytics
2. Set up Crashlytics for error reporting
3. Configure Performance Monitoring
4. Set up Cloud Logging

## Backup Strategy

1. Enable Firestore automatic backups
2. Set up Storage bucket versioning
3. Regular exports of critical data
4. Test restore procedures

## Next Steps

1. Set up CI/CD pipeline
2. Configure staging environment
3. Set up monitoring and alerting
4. Plan for scaling and performance optimization

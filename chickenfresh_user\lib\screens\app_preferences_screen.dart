import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import '../services/notification_service.dart';
import '../widgets/chicken_watermark.dart';

class AppPreferencesScreen extends StatefulWidget {
  const AppPreferencesScreen({super.key});

  @override
  State<AppPreferencesScreen> createState() => _AppPreferencesScreenState();
}

class _AppPreferencesScreenState extends State<AppPreferencesScreen> {
  bool _isDarkMode = false;
  bool _showPricesWithTax = true;
  bool _enableSounds = true;
  bool _enableVibration = true;
  bool _autoRefreshProducts = true;
  String _selectedLanguage = 'English';
  String _selectedCurrency = 'INR (₹)';
  double _textScaleFactor = 1.0;

  final List<String> _languages = ['English', 'Hindi', 'Tamil', 'Telugu', 'Kannada'];
  final List<String> _currencies = ['INR (₹)', 'USD (\$)', 'EUR (€)'];

  @override
  void initState() {
    super.initState();
    _loadPreferences();
  }

  Future<void> _loadPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    setState(() {
      _isDarkMode = prefs.getBool('dark_mode') ?? false;
      _showPricesWithTax = prefs.getBool('show_prices_with_tax') ?? true;
      _enableSounds = prefs.getBool('enable_sounds') ?? true;
      _enableVibration = prefs.getBool('enable_vibration') ?? true;
      _autoRefreshProducts = prefs.getBool('auto_refresh_products') ?? true;
      _selectedLanguage = prefs.getString('selected_language') ?? 'English';
      _selectedCurrency = prefs.getString('selected_currency') ?? 'INR (₹)';
      _textScaleFactor = prefs.getDouble('text_scale_factor') ?? 1.0;
    });
  }

  Future<void> _savePreferences() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setBool('dark_mode', _isDarkMode);
    await prefs.setBool('show_prices_with_tax', _showPricesWithTax);
    await prefs.setBool('enable_sounds', _enableSounds);
    await prefs.setBool('enable_vibration', _enableVibration);
    await prefs.setBool('auto_refresh_products', _autoRefreshProducts);
    await prefs.setString('selected_language', _selectedLanguage);
    await prefs.setString('selected_currency', _selectedCurrency);
    await prefs.setDouble('text_scale_factor', _textScaleFactor);
    
    NotificationService.showSuccessNotification('Preferences saved successfully!');
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('App Preferences'),
        backgroundColor: Colors.red.shade400,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _savePreferences,
          ),
        ],
      ),
      body: ChickenImageWatermark(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Appearance Section
              _buildSection(
                'Appearance',
                Icons.palette,
                Colors.purple,
                [
                  _buildSwitchTile(
                    'Dark Mode',
                    'Use dark theme for the app',
                    Icons.dark_mode,
                    _isDarkMode,
                    (value) => setState(() => _isDarkMode = value),
                  ),
                  _buildSliderTile(
                    'Text Size',
                    'Adjust text size throughout the app',
                    Icons.text_fields,
                    _textScaleFactor,
                    0.8,
                    1.4,
                    (value) => setState(() => _textScaleFactor = value),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Language & Region Section
              _buildSection(
                'Language & Region',
                Icons.language,
                Colors.blue,
                [
                  _buildDropdownTile(
                    'Language',
                    'Select your preferred language',
                    Icons.translate,
                    _selectedLanguage,
                    _languages,
                    (value) => setState(() => _selectedLanguage = value!),
                  ),
                  _buildDropdownTile(
                    'Currency',
                    'Select your preferred currency',
                    Icons.currency_rupee,
                    _selectedCurrency,
                    _currencies,
                    (value) => setState(() => _selectedCurrency = value!),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Display Preferences Section
              _buildSection(
                'Display Preferences',
                Icons.display_settings,
                Colors.green,
                [
                  _buildSwitchTile(
                    'Show Prices with Tax',
                    'Display prices including tax',
                    Icons.receipt,
                    _showPricesWithTax,
                    (value) => setState(() => _showPricesWithTax = value),
                  ),
                  _buildSwitchTile(
                    'Auto Refresh Products',
                    'Automatically refresh product list',
                    Icons.refresh,
                    _autoRefreshProducts,
                    (value) => setState(() => _autoRefreshProducts = value),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Sound & Feedback Section
              _buildSection(
                'Sound & Feedback',
                Icons.volume_up,
                Colors.orange,
                [
                  _buildSwitchTile(
                    'Enable Sounds',
                    'Play sounds for app interactions',
                    Icons.volume_up,
                    _enableSounds,
                    (value) => setState(() => _enableSounds = value),
                  ),
                  _buildSwitchTile(
                    'Enable Vibration',
                    'Vibrate for notifications and feedback',
                    Icons.vibration,
                    _enableVibration,
                    (value) => setState(() => _enableVibration = value),
                  ),
                ],
              ),
              const SizedBox(height: 20),

              // Data & Storage Section
              _buildSection(
                'Data & Storage',
                Icons.storage,
                Colors.red,
                [
                  _buildActionTile(
                    'Clear Cache',
                    'Clear app cache to free up space',
                    Icons.cleaning_services,
                    () => _clearCache(),
                  ),
                  _buildActionTile(
                    'Reset Preferences',
                    'Reset all preferences to default',
                    Icons.restore,
                    () => _showResetDialog(),
                  ),
                ],
              ),
              const SizedBox(height: 32),

              // Save Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton.icon(
                  onPressed: _savePreferences,
                  icon: const Icon(Icons.save),
                  label: const Text('Save Preferences'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Colors.green.shade600,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSection(
    String title,
    IconData icon,
    MaterialColor color,
    List<Widget> children,
  ) {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: color.shade50,
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(icon, color: color.shade600, size: 24),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: color.shade800,
                  ),
                ),
              ],
            ),
          ),
          ...children,
        ],
      ),
    );
  }

  Widget _buildSwitchTile(
    String title,
    String subtitle,
    IconData icon,
    bool value,
    ValueChanged<bool> onChanged,
  ) {
    return ListTile(
      leading: Icon(icon, color: Colors.grey.shade600),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey.shade600,
        ),
      ),
      trailing: Switch(
        value: value,
        onChanged: onChanged,
        activeColor: Colors.green.shade600,
      ),
    );
  }

  Widget _buildDropdownTile(
    String title,
    String subtitle,
    IconData icon,
    String value,
    List<String> options,
    ValueChanged<String?> onChanged,
  ) {
    return ListTile(
      leading: Icon(icon, color: Colors.grey.shade600),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          DropdownButton<String>(
            value: value,
            isExpanded: true,
            items: options.map((String option) {
              return DropdownMenuItem<String>(
                value: option,
                child: Text(option),
              );
            }).toList(),
            onChanged: onChanged,
          ),
        ],
      ),
    );
  }

  Widget _buildSliderTile(
    String title,
    String subtitle,
    IconData icon,
    double value,
    double min,
    double max,
    ValueChanged<double> onChanged,
  ) {
    return ListTile(
      leading: Icon(icon, color: Colors.grey.shade600),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            subtitle,
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey.shade600,
            ),
          ),
          const SizedBox(height: 8),
          Row(
            children: [
              Text('${(value * 100).round()}%'),
              Expanded(
                child: Slider(
                  value: value,
                  min: min,
                  max: max,
                  divisions: 12,
                  onChanged: onChanged,
                  activeColor: Colors.blue.shade600,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionTile(
    String title,
    String subtitle,
    IconData icon,
    VoidCallback onTap,
  ) {
    return ListTile(
      leading: Icon(icon, color: Colors.grey.shade600),
      title: Text(
        title,
        style: const TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: Text(
        subtitle,
        style: TextStyle(
          fontSize: 14,
          color: Colors.grey.shade600,
        ),
      ),
      trailing: Icon(
        Icons.arrow_forward_ios,
        size: 16,
        color: Colors.grey.shade400,
      ),
      onTap: onTap,
    );
  }

  Future<void> _clearCache() async {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Cache'),
        content: const Text('Are you sure you want to clear the app cache?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              NotificationService.showSuccessNotification('Cache cleared successfully!');
            },
            child: const Text('Clear'),
          ),
        ],
      ),
    );
  }

  void _showResetDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Preferences'),
        content: const Text(
          'Are you sure you want to reset all preferences to default? '
          'This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _resetPreferences();
            },
            style: TextButton.styleFrom(
              foregroundColor: Colors.red,
            ),
            child: const Text('Reset'),
          ),
        ],
      ),
    );
  }

  Future<void> _resetPreferences() async {
    final prefs = await SharedPreferences.getInstance();
    
    // Remove all preference keys
    await prefs.remove('dark_mode');
    await prefs.remove('show_prices_with_tax');
    await prefs.remove('enable_sounds');
    await prefs.remove('enable_vibration');
    await prefs.remove('auto_refresh_products');
    await prefs.remove('selected_language');
    await prefs.remove('selected_currency');
    await prefs.remove('text_scale_factor');
    
    // Reset to defaults
    setState(() {
      _isDarkMode = false;
      _showPricesWithTax = true;
      _enableSounds = true;
      _enableVibration = true;
      _autoRefreshProducts = true;
      _selectedLanguage = 'English';
      _selectedCurrency = 'INR (₹)';
      _textScaleFactor = 1.0;
    });
    
    NotificationService.showSuccessNotification('Preferences reset to default!');
  }
}

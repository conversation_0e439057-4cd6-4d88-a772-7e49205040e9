import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

class SettingsService {
  static final SettingsService _instance = SettingsService._internal();
  factory SettingsService() => _instance;
  SettingsService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Get UPI ID from admin settings
  Future<String?> getUpiId() async {
    try {
      final doc = await _firestore.collection('settings').doc('payment').get();
      
      if (doc.exists) {
        final data = doc.data() as Map<String, dynamic>;
        return data['upiId'] as String?;
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting UPI ID: $e');
      }
      return null;
    }
  }

  // Get all payment settings
  Future<Map<String, dynamic>?> getPaymentSettings() async {
    try {
      final doc = await _firestore.collection('settings').doc('payment').get();
      
      if (doc.exists) {
        return doc.data() as Map<String, dynamic>;
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting payment settings: $e');
      }
      return null;
    }
  }
}

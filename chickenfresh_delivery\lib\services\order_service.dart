import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/order.dart' as order_model;
import 'auth_service.dart';
import 'simple_notification_service.dart';

class OrderService {
  static final OrderService _instance = OrderService._internal();
  factory OrderService() => _instance;
  OrderService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AuthService _authService = AuthService();
  final SimpleNotificationService _notificationService = SimpleNotificationService();

  // Monitor for new order assignments and create notifications
  void startOrderAssignmentMonitoring() async {
    try {
      final deliveryPersonId = await _getDeliveryPersonId();
      if (deliveryPersonId == null) return;

      // Listen for orders assigned to this delivery person
      _firestore
          .collection('orders')
          .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
          .where('status', isEqualTo: order_model.OrderStatus.outForDelivery.name)
          .snapshots()
          .listen((snapshot) {
        for (final change in snapshot.docChanges) {
          if (change.type == DocumentChangeType.added) {
            // New order assigned - create notification
            try {
              final order = order_model.Order.fromFirestore(change.doc);
              _notificationService.notifyNewOrder(
                order.orderNumber,
                order.customerInfo.name,
                order.totalAmount,
              );

              if (kDebugMode) {
                print('New order notification created: ${order.orderNumber}');
              }
            } catch (e) {
              if (kDebugMode) {
                print('Error creating notification for new order: $e');
              }
            }
          }
        }
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error starting order assignment monitoring: $e');
      }
    }
  }

  // Get orders assigned to current delivery person
  Stream<List<order_model.Order>> getAssignedOrders() {
    return Stream.fromFuture(_authService.isAuthenticated).asyncExpand((isAuth) {
      if (!isAuth) {
        return Stream.value([]);
      }

      // Get delivery person ID from local storage or Firebase Auth
      return Stream.fromFuture(_getDeliveryPersonId()).asyncExpand((deliveryPersonId) {
        if (deliveryPersonId == null) {
          return Stream.value([]);
        }

        return _firestore
            .collection('orders')
            .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
            .where('status', whereIn: [
              order_model.OrderStatus.outForDelivery.name,
              order_model.OrderStatus.processing.name, // Include processing orders too
            ])
            .orderBy('createdAt', descending: true)
            .snapshots()
            .map((snapshot) {
              if (kDebugMode) {
                print('Found ${snapshot.docs.length} orders for delivery person $deliveryPersonId');
              }
              return snapshot.docs
                  .map((doc) {
                    try {
                      if (kDebugMode) {
                        print('Raw order data for ${doc.id}: ${doc.data()}');
                      }
                      final order = order_model.Order.fromFirestore(doc);
                      if (kDebugMode) {
                        print('Parsed Order ${order.orderNumber}: ${order.customerInfo.name} - ${order.deliveryAddress.fullAddress}');
                        print('Items count: ${order.items.length}, Total: ${order.totalAmount}');
                        print('Items: ${order.items.map((item) => '${item.productName} x${item.quantity} @ ₹${item.price}').join(', ')}');
                      }
                      return order;
                    } catch (e) {
                      if (kDebugMode) {
                        print('Error parsing order ${doc.id}: $e');
                        print('Raw data: ${doc.data()}');
                      }
                      rethrow;
                    }
                  })
                  .toList();
            });
      });
    });
  }

  // Get delivery person ID from Firebase Auth or local storage
  Future<String?> _getDeliveryPersonId() async {
    try {
      // Try to get ID from Firebase Auth first
      if (_authService.currentUser != null) {
        return _authService.currentUser!.uid;
      } else {
        // Get ID from local storage for admin-authenticated users
        final deliveryPersonInfo = await _authService.getSavedDeliveryPersonInfo();
        return deliveryPersonInfo?['id'];
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error getting delivery person ID: $e');
      }
      return null;
    }
  }

  // Get specific order details
  Future<order_model.Order?> getOrder(String orderId) async {
    try {
      if (!(await _authService.isAuthenticated)) {
        throw Exception('Authentication required');
      }

      final deliveryPersonId = await _getDeliveryPersonId();
      if (deliveryPersonId == null) {
        throw Exception('Delivery person ID not found');
      }

      final doc = await _firestore.collection('orders').doc(orderId).get();

      if (!doc.exists) {
        if (kDebugMode) {
          print('Order $orderId not found');
        }
        return null;
      }

      if (kDebugMode) {
        print('Fetching order $orderId data: ${doc.data()}');
      }

      final order = order_model.Order.fromFirestore(doc);

      // Verify this order is assigned to current delivery person
      if (order.assignedDeliveryPersonId != deliveryPersonId) {
        throw Exception('You are not authorized to view this order');
      }

      if (kDebugMode) {
        print('Order details: ${order.orderNumber}, Customer: ${order.customerInfo.name}, Address: ${order.deliveryAddress.fullAddress}');
      }

      return order;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting order $orderId: $e');
      }
      rethrow;
    }
  }

  // Update order status (matches admin app exactly)
  Future<void> updateOrderStatus(String orderId, order_model.OrderStatus newStatus) async {
    try {
      if (!(await _authService.isAuthenticated)) {
        throw Exception('Authentication required');
      }

      final deliveryPersonId = await _getDeliveryPersonId();
      if (deliveryPersonId == null) {
        throw Exception('Delivery person ID not found');
      }

      // Get the current order to verify assignment and check format
      final orderDoc = await _firestore.collection('orders').doc(orderId).get();
      if (!orderDoc.exists) {
        throw Exception('Order not found');
      }

      final orderData = orderDoc.data() as Map<String, dynamic>;
      final assignedDeliveryPersonId = orderData['assignedDeliveryPersonId'];

      // Verify this order is assigned to current delivery person
      if (assignedDeliveryPersonId != deliveryPersonId) {
        throw Exception('You are not authorized to update this order');
      }

      // Allow delivery person to update status to delivered or other valid statuses
      final allowedStatuses = [
        order_model.OrderStatus.delivered,
        order_model.OrderStatus.outForDelivery, // Allow to update back to out for delivery if needed
      ];

      if (!allowedStatuses.contains(newStatus)) {
        throw Exception('You can only mark orders as delivered or out for delivery');
      }

      // Use EXACT same structure as admin app
      bool isLegacyFormat = orderData.containsKey('userName') || orderData.containsKey('userPhone');

      final updateData = <String, dynamic>{
        'status': newStatus.name, // Exact same field name as admin
      };

      // For legacy orders, we need to add the updatedAt field since it might not exist
      if (isLegacyFormat) {
        updateData['updatedAt'] = FieldValue.serverTimestamp();
        // Also add createdAt if it doesn't exist (use timestamp field)
        if (!orderData.containsKey('createdAt') && orderData.containsKey('timestamp')) {
          updateData['createdAt'] = orderData['timestamp'];
        }
      } else {
        updateData['updatedAt'] = FieldValue.serverTimestamp();
      }

      // Add delivery date if status is delivered (EXACT same as admin)
      if (newStatus == order_model.OrderStatus.delivered) {
        updateData['deliveryDate'] = FieldValue.serverTimestamp();
      }

      if (kDebugMode) {
        print('Updating order $orderId with data: $updateData');
      }

      await _firestore
          .collection('orders')
          .doc(orderId)
          .update(updateData);

      // Create notification for successful delivery
      if (newStatus == order_model.OrderStatus.delivered) {
        final order = await getOrder(orderId);
        if (order != null) {
          _notificationService.notifyDeliverySuccess(
            order.orderNumber,
            order.customerInfo.name,
          );
        }
      }

      if (kDebugMode) {
        print('Order $orderId status updated to ${newStatus.name} successfully');

        // Verify the update by reading the document back (same as admin)
        final updatedDoc = await _firestore.collection('orders').doc(orderId).get();
        if (updatedDoc.exists) {
          final updatedData = updatedDoc.data() as Map<String, dynamic>;
          print('Verified: Order $orderId now has status: ${updatedData['status']}');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating order status: $e');
      }
      rethrow;
    }
  }

  // Update payment collection status (matches admin app exactly)
  Future<void> updatePaymentStatus(String orderId, order_model.PaymentMethod paymentMethod, bool collected) async {
    try {
      if (!(await _authService.isAuthenticated)) {
        throw Exception('Authentication required');
      }

      // Verify order assignment
      final order = await getOrder(orderId);
      if (order == null) {
        throw Exception('Order not found');
      }

      // Use exact same field names as admin app
      final updateData = <String, dynamic>{
        'paymentMethod': paymentMethod.name, // Exact same field name
        'paymentCollected': collected,       // Exact same field name
        'updatedAt': FieldValue.serverTimestamp(), // Exact same field name
      };

      await _firestore
          .collection('orders')
          .doc(orderId)
          .update(updateData);

      // Create payment reminder notification if not collected
      if (!collected) {
        _notificationService.notifyPaymentReminder(
          order.orderNumber,
          order.totalAmount,
        );
      }

      if (kDebugMode) {
        print('Payment status updated for order $orderId: ${paymentMethod.name} - $collected');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating payment status: $e');
      }
      rethrow;
    }
  }

  // Cancel order (delivery person can cancel if customer refuses)
  Future<void> cancelOrder(String orderId, String reason) async {
    try {
      if (!(await _authService.isAuthenticated)) {
        throw Exception('Authentication required');
      }

      final deliveryPersonId = await _getDeliveryPersonId();
      if (deliveryPersonId == null) {
        throw Exception('Delivery person ID not found');
      }

      // Get the current order to verify assignment
      final orderDoc = await _firestore.collection('orders').doc(orderId).get();
      if (!orderDoc.exists) {
        throw Exception('Order not found');
      }

      final orderData = orderDoc.data() as Map<String, dynamic>;
      final assignedDeliveryPersonId = orderData['assignedDeliveryPersonId'];

      // Verify this order is assigned to current delivery person
      if (assignedDeliveryPersonId != deliveryPersonId) {
        throw Exception('You are not authorized to cancel this order');
      }

      // Check if order can be cancelled
      final currentStatus = orderData['status'];
      if (currentStatus == 'delivered') {
        throw Exception('Cannot cancel a delivered order');
      }

      final updateData = <String, dynamic>{
        'status': order_model.OrderStatus.cancelled.name,
        'cancellationReason': reason,
        'updatedAt': FieldValue.serverTimestamp(),
      };

      await _firestore
          .collection('orders')
          .doc(orderId)
          .update(updateData);

      if (kDebugMode) {
        print('Order $orderId cancelled successfully by delivery person');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error cancelling order: $e');
      }
      rethrow;
    }
  }

  // Partial cancel order items (delivery person can cancel specific items if unavailable)
  Future<void> partialCancelOrderByIndices(
    String orderId,
    List<int> itemIndicesToCancel,
    String reason
  ) async {
    try {
      if (!(await _authService.isAuthenticated)) {
        throw Exception('Authentication required');
      }

      final deliveryPersonId = await _getDeliveryPersonId();
      if (deliveryPersonId == null) {
        throw Exception('Delivery person ID not found');
      }

      final order = await getOrder(orderId);
      if (order == null) {
        throw Exception('Order not found');
      }

      // Verify this order is assigned to current delivery person
      if (order.assignedDeliveryPersonId != deliveryPersonId) {
        throw Exception('You are not authorized to modify this order');
      }

      // Check if order can be modified
      if (order.status == order_model.OrderStatus.delivered) {
        throw Exception('Cannot modify a delivered order');
      }

      // Calculate new totals after removing cancelled items by indices
      final remainingItems = <order_model.OrderItem>[];
      final cancelledItems = <order_model.OrderItem>[];

      for (int i = 0; i < order.items.length; i++) {
        if (itemIndicesToCancel.contains(i)) {
          cancelledItems.add(order.items[i]);
        } else {
          remainingItems.add(order.items[i]);
        }
      }

      if (remainingItems.isEmpty) {
        // If all items are cancelled, cancel the entire order
        await cancelOrder(orderId, reason);
        return;
      }

      // Calculate new totals
      final newSubtotal = remainingItems.fold(
        0.0,
        (total, item) => total + (item.price * item.quantity)
      );
      final newTotalAmount = newSubtotal + order.deliveryFee;

      // Update order with remaining items
      final updateData = <String, dynamic>{
        'items': remainingItems.map((item) => item.toMap()).toList(),
        'subtotal': newSubtotal,
        'totalAmount': newTotalAmount,
        'status': order_model.OrderStatus.partialCancelled.name,
        'cancellationReason': reason,
        'cancelledItems': cancelledItems.map((item) => item.toMap()).toList(),
        'updatedAt': FieldValue.serverTimestamp(),
      };

      await _firestore
          .collection('orders')
          .doc(orderId)
          .update(updateData);

      if (kDebugMode) {
        print('Order $orderId partially cancelled successfully by delivery person');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error partially cancelling order: $e');
      }
      rethrow;
    }
  }

  // Get delivery statistics for current delivery person
  Future<Map<String, int>> getDeliveryStats() async {
    try {
      if (!(await _authService.isAuthenticated)) {
        return {'total': 0, 'today': 0, 'pending': 0};
      }

      final deliveryPersonId = _authService.currentUser!.uid;
      final today = DateTime.now();
      final startOfDay = DateTime(today.year, today.month, today.day);

      // Get total deliveries
      final totalQuery = await _firestore
          .collection('orders')
          .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
          .where('status', isEqualTo: order_model.OrderStatus.delivered.name)
          .get();

      // Get today's deliveries
      final todayQuery = await _firestore
          .collection('orders')
          .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
          .where('status', isEqualTo: order_model.OrderStatus.delivered.name)
          .where('deliveryDate', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .get();

      // Get pending deliveries
      final pendingQuery = await _firestore
          .collection('orders')
          .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
          .where('status', isEqualTo: order_model.OrderStatus.outForDelivery.name)
          .get();

      return {
        'total': totalQuery.docs.length,
        'today': todayQuery.docs.length,
        'pending': pendingQuery.docs.length,
      };
    } catch (e) {
      if (kDebugMode) {
        print('Error getting delivery stats: $e');
      }
      return {'total': 0, 'today': 0, 'pending': 0};
    }
  }

  // Get all delivered orders for current delivery person
  Future<List<order_model.Order>> getDeliveredOrders() async {
    try {
      final deliveryPersonId = await _getDeliveryPersonId();
      if (deliveryPersonId == null) {
        return [];
      }

      final querySnapshot = await _firestore
          .collection('orders')
          .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
          .where('status', isEqualTo: order_model.OrderStatus.delivered.name)
          .orderBy('deliveredAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => order_model.Order.fromFirestore(doc))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting delivered orders: $e');
      }
      return [];
    }
  }

  // Get today's delivered orders for current delivery person
  Future<List<order_model.Order>> getTodayDeliveredOrders() async {
    try {
      final deliveryPersonId = await _getDeliveryPersonId();
      if (deliveryPersonId == null) {
        return [];
      }

      final now = DateTime.now();
      final startOfDay = DateTime(now.year, now.month, now.day);
      final endOfDay = DateTime(now.year, now.month, now.day, 23, 59, 59);

      final querySnapshot = await _firestore
          .collection('orders')
          .where('assignedDeliveryPersonId', isEqualTo: deliveryPersonId)
          .where('status', isEqualTo: order_model.OrderStatus.delivered.name)
          .where('deliveredAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startOfDay))
          .where('deliveredAt', isLessThanOrEqualTo: Timestamp.fromDate(endOfDay))
          .orderBy('deliveredAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => order_model.Order.fromFirestore(doc))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting today\'s delivered orders: $e');
      }
      return [];
    }
  }

  // Get available status options for delivery person
  List<order_model.OrderStatus> getAvailableStatusOptions(order_model.OrderStatus currentStatus) {
    switch (currentStatus) {
      case order_model.OrderStatus.outForDelivery:
        return [
          order_model.OrderStatus.delivered,
          // Allow to mark back to out for delivery if there are issues
        ];
      case order_model.OrderStatus.delivered:
        return []; // Cannot change from delivered
      default:
        return [order_model.OrderStatus.outForDelivery]; // Can only set to out for delivery
    }
  }

  // Get status display name for delivery app (matches admin app exactly)
  String getStatusDisplayName(order_model.OrderStatus status) {
    switch (status) {
      case order_model.OrderStatus.pending:
        return 'Pending';
      case order_model.OrderStatus.confirmed:
        return 'Confirmed';
      case order_model.OrderStatus.processing:
        return 'Processing';
      case order_model.OrderStatus.outForDelivery:
        return 'Out for Delivery';
      case order_model.OrderStatus.delivered:
        return 'Delivered';
      case order_model.OrderStatus.cancelled:
        return 'Cancelled';
      case order_model.OrderStatus.partialCancelled:
        return 'Partial Cancelled';
      case order_model.OrderStatus.refunded:
        return 'Refunded';
    }
  }

  // Add delivery notes
  Future<void> addDeliveryNotes(String orderId, String notes) async {
    try {
      if (!(await _authService.isAuthenticated)) {
        throw Exception('Authentication required');
      }

      final deliveryPersonId = await _getDeliveryPersonId();
      if (deliveryPersonId == null) {
        throw Exception('Delivery person ID not found');
      }

      await _firestore
          .collection('orders')
          .doc(orderId)
          .update({
            'deliveryNotes': notes,
            'deliveryNotesAddedBy': deliveryPersonId,
            'deliveryNotesAddedAt': FieldValue.serverTimestamp(),
            'updatedAt': FieldValue.serverTimestamp(),
          });

      if (kDebugMode) {
        print('Delivery notes added to order $orderId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error adding delivery notes: $e');
      }
      rethrow;
    }
  }
}

import 'package:flutter/material.dart';
import 'dart:async';
import '../services/auth_service.dart';
import '../models/delivery_person.dart';
import 'login_screen.dart';
import 'home_screen.dart';

class AuthWrapper extends StatefulWidget {
  const AuthWrapper({super.key});

  @override
  State<AuthWrapper> createState() => _AuthWrapperState();
}

class _AuthWrapperState extends State<AuthWrapper> {
  DeliveryPerson? _currentDeliveryPerson;
  bool _isLoading = true;
  Timer? _authCheckTimer;

  @override
  void initState() {
    super.initState();
    _checkAuthStatus();
    _startPeriodicAuthCheck();
  }

  @override
  void dispose() {
    _authCheckTimer?.cancel();
    super.dispose();
  }

  void _startPeriodicAuthCheck() {
    // Check auth status every 2 seconds to catch changes quickly
    _authCheckTimer = Timer.periodic(const Duration(seconds: 2), (_) {
      _checkAuthStatus();
    });
  }

  Future<void> _checkAuthStatus() async {
    try {
      final deliveryPerson = await AuthService().getCurrentDeliveryPerson();
      if (mounted) {
        setState(() {
          _currentDeliveryPerson = deliveryPerson;
          _isLoading = false;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _currentDeliveryPerson = null;
          _isLoading = false;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Scaffold(
        body: Center(
          child: CircularProgressIndicator(),
        ),
      );
    }

    if (_currentDeliveryPerson != null) {
      return const HomeScreen();
    } else {
      return const LoginScreen();
    }
  }
}

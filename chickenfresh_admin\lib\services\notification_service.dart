import 'dart:async';

/// Legacy notification service - deprecated
/// Use SimpleNotificationService instead
@Deprecated('Use SimpleNotificationService instead')
class NotificationService {
  static final NotificationService _instance = NotificationService._internal();
  factory NotificationService() => _instance;
  NotificationService._internal();

  Timer? _followUpTimer;
  final List<String> _activeNotifications = [];
  final StreamController<List<String>> _notificationController =
      StreamController<List<String>>.broadcast();

  /// Stream of active notifications
  Stream<List<String>> get notificationStream => _notificationController.stream;

  /// Initialize notification service
  void initialize() {
    // Legacy method - does nothing
  }

  /// Dispose resources
  void dispose() {
    _followUpTimer?.cancel();
    _notificationController.close();
  }

  /// Add notification (legacy)
  void addNotification(String notification) {
    _activeNotifications.add(notification);
    _notificationController.add(List.from(_activeNotifications));
  }

  /// Remove notification (legacy)
  void removeNotification(String notificationId) {
    _activeNotifications.removeWhere((n) => n == notificationId);
    _notificationController.add(List.from(_activeNotifications));
  }

  /// Clear all notifications (legacy)
  void clearAllNotifications() {
    _activeNotifications.clear();
    _notificationController.add(List.from(_activeNotifications));
  }
}

/// Legacy notification types
enum NotificationType {
  newOrder,
  orderFollowUp,
  orderDelivered,
  stockAlert,
  systemAlert,
}

enum NotificationPriority {
  low,
  medium,
  high,
  critical,
}

/// Legacy notification class
@Deprecated('Use SimpleNotification instead')
class AdminNotification {
  final String id;
  final NotificationType type;
  final String title;
  final String message;
  final NotificationPriority priority;
  final DateTime createdAt;
  final bool isRead;

  AdminNotification({
    required this.id,
    required this.type,
    required this.title,
    required this.message,
    required this.priority,
    required this.createdAt,
    this.isRead = false,
  });
}

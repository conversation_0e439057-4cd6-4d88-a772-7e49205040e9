// Copy the order model from admin app for consistency
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

enum OrderStatus {
  pending,
  confirmed,
  processing,
  outForDelivery,
  delivered,
  cancelled,
  partialCancelled,
  refunded
}

enum PaymentMethod {
  cash,
  upi,
  card
}

class OrderItem {
  final String productId;
  final String productName;
  final int quantity;
  final double price;
  final String? imageUrl;

  OrderItem({
    required this.productId,
    required this.productName,
    required this.quantity,
    required this.price,
    this.imageUrl,
  });

  factory OrderItem.fromMap(Map<String, dynamic> data) {
    if (kDebugMode) {
      print('Parsing OrderItem from data: $data');
    }

    // Try multiple field name variations
    final productId = data['productId'] ?? data['product_id'] ?? data['id'] ?? '';
    final productName = data['productName'] ?? data['product_name'] ?? data['name'] ?? '';
    final quantity = data['quantity'] ?? data['qty'] ?? 0;
    final price = (data['price'] ?? data['unitPrice'] ?? data['unit_price'] ?? 0).toDouble();
    final imageUrl = data['imageUrl'] ?? data['image_url'] ?? data['image'];

    if (kDebugMode) {
      print('Parsed item: $productName x$quantity @ ₹$price');
    }

    return OrderItem(
      productId: productId,
      productName: productName,
      quantity: quantity,
      price: price,
      imageUrl: imageUrl,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'productId': productId,
      'productName': productName,
      'quantity': quantity,
      'price': price,
      'imageUrl': imageUrl,
    };
  }

  double get totalPrice => price * quantity;
}

class CustomerInfo {
  final String name;
  final String phoneNumber;
  final String? email;

  CustomerInfo({
    required this.name,
    required this.phoneNumber,
    this.email,
  });

  factory CustomerInfo.fromMap(Map<String, dynamic> data) {
    return CustomerInfo(
      name: data['name'] ?? data['customerName'] ?? data['userName'] ?? 'Unknown Customer',
      phoneNumber: data['phoneNumber'] ?? data['customerPhone'] ?? data['userPhone'] ?? '',
      email: data['email'] ?? data['customerEmail'] ?? data['userEmail'],
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'name': name,
      'phoneNumber': phoneNumber,
      'email': email,
    };
  }
}

class DeliveryAddress {
  final String apartmentName;
  final String blockName;
  final String houseNumber;

  DeliveryAddress({
    required this.apartmentName,
    required this.blockName,
    required this.houseNumber,
  });

  factory DeliveryAddress.fromMap(Map<String, dynamic> data) {
    return DeliveryAddress(
      apartmentName: data['apartmentName'] ?? '',
      blockName: data['blockName'] ?? '',
      houseNumber: data['houseNumber'] ?? '',
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'apartmentName': apartmentName,
      'blockName': blockName,
      'houseNumber': houseNumber,
    };
  }

  String get fullAddress => '$houseNumber, $blockName, $apartmentName';
}

class Order {
  final String id;
  final String orderNumber;
  final List<OrderItem> items;
  final double subtotal;
  final double deliveryFee;
  final double totalAmount;
  final OrderStatus status;
  final CustomerInfo customerInfo;
  final DeliveryAddress deliveryAddress;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? deliveryDate;
  final String? notes;
  final String? cancellationReason;
  final String? assignedDeliveryPersonId;
  final String? assignedDeliveryPersonName; // Add this field to match admin app
  final PaymentMethod? paymentMethod;
  final bool? paymentCollected;

  Order({
    required this.id,
    required this.orderNumber,
    required this.items,
    required this.subtotal,
    this.deliveryFee = 0.0,
    required this.totalAmount,
    this.status = OrderStatus.pending,
    required this.customerInfo,
    required this.deliveryAddress,
    required this.createdAt,
    required this.updatedAt,
    this.deliveryDate,
    this.notes,
    this.cancellationReason,
    this.assignedDeliveryPersonId,
    this.assignedDeliveryPersonName,
    this.paymentMethod,
    this.paymentCollected,
  });

  factory Order.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    try {
      if (kDebugMode) {
        print('Parsing order ${doc.id} with data keys: ${data.keys.toList()}');
      }

      // Check if this is legacy format (from user app) or new format (from admin app)
      bool isLegacyFormat = data.containsKey('userName') || data.containsKey('userPhone') || data.containsKey('totalPrice');

      if (isLegacyFormat) {
        return _fromLegacyFormat(doc.id, data);
      }

      // Parse items for new admin format
      final itemsData = data['items'] as List<dynamic>? ?? [];
      if (kDebugMode) {
        print('Items data: $itemsData');
      }
      final items = itemsData.map((item) => OrderItem.fromMap(item as Map<String, dynamic>)).toList();

      // Calculate totals - try multiple field names with better fallbacks
      double subtotal = (data['subtotal'] ?? data['subTotal'] ?? data['sub_total'] ?? 0).toDouble();
      double deliveryFee = (data['deliveryFee'] ?? data['delivery_fee'] ?? data['deliveryCharge'] ?? 0).toDouble();
      double totalAmount = (data['totalAmount'] ?? data['total_amount'] ?? data['totalPrice'] ?? data['total_price'] ?? data['total'] ?? 0).toDouble();

      // Calculate from items if amounts are missing or zero
      if (items.isNotEmpty) {
        double calculatedSubtotal = items.fold(0.0, (total, item) => total + item.totalPrice);

        // If totalAmount is 0 or missing, calculate it
        if (totalAmount == 0) {
          totalAmount = calculatedSubtotal + deliveryFee;
          subtotal = calculatedSubtotal;
        }

        // If subtotal is 0 or missing, calculate it
        if (subtotal == 0) {
          subtotal = totalAmount - deliveryFee;
          // Ensure subtotal is not negative
          if (subtotal < 0) {
            subtotal = calculatedSubtotal;
            totalAmount = subtotal + deliveryFee;
          }
        }

        // Validate calculations - if calculated subtotal differs significantly, use calculated values
        if ((calculatedSubtotal - subtotal).abs() > 0.01 && calculatedSubtotal > 0) {
          if (kDebugMode) {
            print('Order ${doc.id}: Using calculated subtotal $calculatedSubtotal instead of stored $subtotal');
          }
          subtotal = calculatedSubtotal;
          totalAmount = subtotal + deliveryFee;
        }
      }

      if (kDebugMode) {
        print('Parsed ${items.length} items, subtotal: $subtotal, deliveryFee: $deliveryFee, total: $totalAmount');
      }

      // Parse timestamps
      final createdAt = _parseTimestamp(data['createdAt']);
      final updatedAt = _parseTimestamp(data['updatedAt']);

      // Generate order number from document ID
      String orderNumber = data['orderNumber'] ?? doc.id.substring(0, 8).toUpperCase();

      // Parse customer info with comprehensive fallback for legacy format
      CustomerInfo customerInfo;
      if (data['customerInfo'] != null) {
        customerInfo = CustomerInfo.fromMap(data['customerInfo'] as Map<String, dynamic>);
      } else {
        // Legacy format fallback with more field variations
        customerInfo = CustomerInfo(
          name: data['userName'] ?? data['customerName'] ?? data['name'] ?? data['user_name'] ?? data['customer_name'] ?? 'Unknown Customer',
          phoneNumber: data['userPhone'] ?? data['customerPhone'] ?? data['phoneNumber'] ?? data['phone_number'] ?? data['phone'] ?? '',
          email: data['userEmail'] ?? data['customerEmail'] ?? data['email'] ?? data['user_email'] ?? data['customer_email'],
        );
      }

      // Parse delivery address with fallback for legacy format
      DeliveryAddress deliveryAddress;
      if (data['deliveryAddress'] != null) {
        deliveryAddress = DeliveryAddress.fromMap(data['deliveryAddress'] as Map<String, dynamic>);
      } else {
        // Legacy format fallback
        deliveryAddress = DeliveryAddress(
          apartmentName: data['apartmentName'] ?? '',
          blockName: data['blockName'] ?? '',
          houseNumber: data['houseNumber'] ?? '',
        );
      }

      final order = Order(
        id: doc.id,
        orderNumber: orderNumber,
        items: items,
        subtotal: subtotal,
        deliveryFee: deliveryFee,
        totalAmount: totalAmount,
        status: _parseOrderStatus(data['status']),
        customerInfo: customerInfo,
        deliveryAddress: deliveryAddress,
        createdAt: createdAt,
        updatedAt: updatedAt,
        deliveryDate: data['deliveryDate'] != null ? _parseTimestamp(data['deliveryDate']) : null,
        notes: data['notes'],
        cancellationReason: data['cancellationReason'],
        assignedDeliveryPersonId: data['assignedDeliveryPersonId'],
        assignedDeliveryPersonName: data['assignedDeliveryPersonName'],
        paymentMethod: _parsePaymentMethod(data['paymentMethod']),
        paymentCollected: data['paymentCollected'],
      );

      // Validate the parsed order data in debug mode
      order.validateOrderData();

      return order;
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing order ${doc.id}: $e');
        print('Order data: $data');
      }
      rethrow;
    }
  }

  static OrderStatus _parseOrderStatus(dynamic status) {
    if (status is String) {
      try {
        return OrderStatus.values.firstWhere(
          (e) => e.name == status,
          orElse: () => OrderStatus.pending,
        );
      } catch (e) {
        return OrderStatus.pending;
      }
    }
    return OrderStatus.pending;
  }

  static PaymentMethod? _parsePaymentMethod(dynamic method) {
    if (method is String) {
      try {
        return PaymentMethod.values.firstWhere(
          (e) => e.name == method,
        );
      } catch (e) {
        return null;
      }
    }
    return null;
  }

  static DateTime _parseTimestamp(dynamic timestamp) {
    if (timestamp is Timestamp) {
      return timestamp.toDate();
    }
    if (timestamp is DateTime) {
      return timestamp;
    }
    if (timestamp is String) {
      try {
        return DateTime.parse(timestamp);
      } catch (e) {
        return DateTime.now();
      }
    }
    return DateTime.now();
  }

  // Parse legacy format orders (from user app)
  static Order _fromLegacyFormat(String docId, Map<String, dynamic> data) {
    if (kDebugMode) {
      print('Parsing legacy format order $docId');
    }

    // Parse items from legacy format with comprehensive field mapping
    List<OrderItem> items = [];
    if (data['items'] != null) {
      final itemsData = data['items'] as List<dynamic>;
      if (kDebugMode) {
        print('Legacy format items data: $itemsData');
      }
      items = itemsData.map((item) {
        final itemMap = item as Map<String, dynamic>;
        if (kDebugMode) {
          print('Parsing legacy item: $itemMap');
        }
        return OrderItem(
          productId: itemMap['id'] ?? itemMap['productId'] ?? itemMap['product_id'] ?? '',
          productName: itemMap['name'] ?? itemMap['productName'] ?? itemMap['product_name'] ?? itemMap['title'] ?? '',
          quantity: itemMap['quantity'] ?? itemMap['qty'] ?? 1,
          price: (itemMap['price'] ?? itemMap['unitPrice'] ?? itemMap['unit_price'] ?? 0).toDouble(),
          imageUrl: itemMap['imageUrl'] ?? itemMap['image_url'] ?? itemMap['image'],
        );
      }).toList();
    } else if (data['products'] != null) {
      // Handle alternative legacy format with 'products' field
      final productsData = data['products'] as List<dynamic>;
      if (kDebugMode) {
        print('Legacy format products data: $productsData');
      }
      items = productsData.map((product) {
        final productMap = product as Map<String, dynamic>;
        if (kDebugMode) {
          print('Parsing legacy product: $productMap');
        }
        return OrderItem(
          productId: productMap['id'] ?? productMap['productId'] ?? '',
          productName: productMap['name'] ?? productMap['productName'] ?? productMap['title'] ?? '',
          quantity: productMap['quantity'] ?? productMap['qty'] ?? 1,
          price: (productMap['price'] ?? productMap['unitPrice'] ?? 0).toDouble(),
          imageUrl: productMap['imageUrl'] ?? productMap['image'],
        );
      }).toList();
    }

    // Calculate totals with better fallbacks
    double totalAmount = (data['totalPrice'] ?? data['totalAmount'] ?? data['total_amount'] ?? data['total'] ?? 0).toDouble();

    // If totalAmount is still 0, calculate from items
    if (totalAmount == 0 && items.isNotEmpty) {
      totalAmount = items.fold(0.0, (total, item) => total + item.totalPrice);
    }

    double subtotal = totalAmount; // No separate subtotal in legacy format

    // Parse timestamp
    DateTime createdAt = DateTime.now();
    if (data['timestamp'] != null) {
      createdAt = _parseTimestamp(data['timestamp']);
    }

    // Generate order number from document ID
    String orderNumber = docId.substring(0, 8).toUpperCase();

    final order = Order(
      id: docId,
      orderNumber: orderNumber,
      items: items,
      subtotal: subtotal,
      deliveryFee: 0.0, // No delivery fee in legacy format
      totalAmount: totalAmount,
      status: _parseOrderStatus(data['status']),
      customerInfo: CustomerInfo(
        name: data['userName'] ?? data['customerName'] ?? data['name'] ?? data['user_name'] ?? data['customer_name'] ?? 'Unknown Customer',
        phoneNumber: data['userPhone'] ?? data['customerPhone'] ?? data['phoneNumber'] ?? data['phone_number'] ?? data['phone'] ?? '',
        email: data['userEmail'] ?? data['customerEmail'] ?? data['email'] ?? data['user_email'] ?? data['customer_email'],
      ),
      deliveryAddress: DeliveryAddress(
        apartmentName: data['apartmentName'] ?? '',
        blockName: data['blockName'] ?? '',
        houseNumber: data['houseNumber'] ?? '',
      ),
      createdAt: createdAt,
      updatedAt: data['updatedAt'] != null ? _parseTimestamp(data['updatedAt']) : createdAt,
      deliveryDate: data['deliveryDate'] != null ? _parseTimestamp(data['deliveryDate']) : null,
      notes: data['notes'],
      cancellationReason: data['cancellationReason'],
      assignedDeliveryPersonId: data['assignedDeliveryPersonId'],
      assignedDeliveryPersonName: data['assignedDeliveryPersonName'],
      paymentMethod: _parsePaymentMethod(data['paymentMethod']),
      paymentCollected: data['paymentCollected'],
    );

    // Validate the parsed legacy order data
    order.validateOrderData();

    return order;
  }

  // Business logic methods
  bool get canUpdateStatus => 
      status == OrderStatus.outForDelivery;
  
  bool get isCompleted => 
      status == OrderStatus.delivered || 
      status == OrderStatus.cancelled || 
      status == OrderStatus.refunded;
  
  bool get isActive => !isCompleted;
  
  String get statusDisplayName {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.processing:
        return 'Processing';
      case OrderStatus.outForDelivery:
        return 'Out for Delivery';
      case OrderStatus.delivered:
        return 'Delivered';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.partialCancelled:
        return 'Partial Cancelled';
      case OrderStatus.refunded:
        return 'Refunded';
    }
  }

  String get formattedTotalAmount => '₹${totalAmount.toStringAsFixed(2)}';

  int get totalItems => items.fold(0, (total, item) => total + item.quantity);

  // Validation method to check for data issues
  void validateOrderData() {
    if (kDebugMode) {
      List<String> issues = [];

      if (customerInfo.name.isEmpty || customerInfo.name == 'Unknown Customer') {
        issues.add('Customer name is missing or unknown');
      }

      if (customerInfo.phoneNumber.isEmpty) {
        issues.add('Customer phone number is missing');
      }

      if (totalAmount <= 0) {
        issues.add('Total amount is zero or negative: $totalAmount');
      }

      if (items.isEmpty) {
        issues.add('No order items found');
      } else {
        for (int i = 0; i < items.length; i++) {
          final item = items[i];
          if (item.productName.isEmpty) {
            issues.add('Item $i has no product name');
          }
          if (item.price <= 0) {
            issues.add('Item $i has invalid price: ${item.price}');
          }
          if (item.quantity <= 0) {
            issues.add('Item $i has invalid quantity: ${item.quantity}');
          }
        }
      }

      if (issues.isNotEmpty) {
        print('Order $id validation issues:');
        for (String issue in issues) {
          print('  - $issue');
        }
      }
    }
  }

  // Create a copy of the order with updated fields
  Order copyWith({
    String? id,
    String? orderNumber,
    List<OrderItem>? items,
    double? subtotal,
    double? deliveryFee,
    double? totalAmount,
    OrderStatus? status,
    CustomerInfo? customerInfo,
    DeliveryAddress? deliveryAddress,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? deliveryDate,
    String? notes,
    String? cancellationReason,
    String? assignedDeliveryPersonId,
    PaymentMethod? paymentMethod,
    bool? paymentCollected,
  }) {
    return Order(
      id: id ?? this.id,
      orderNumber: orderNumber ?? this.orderNumber,
      items: items ?? this.items,
      subtotal: subtotal ?? this.subtotal,
      deliveryFee: deliveryFee ?? this.deliveryFee,
      totalAmount: totalAmount ?? this.totalAmount,
      status: status ?? this.status,
      customerInfo: customerInfo ?? this.customerInfo,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      deliveryDate: deliveryDate ?? this.deliveryDate,
      notes: notes ?? this.notes,
      cancellationReason: cancellationReason ?? this.cancellationReason,
      assignedDeliveryPersonId: assignedDeliveryPersonId ?? this.assignedDeliveryPersonId,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentCollected: paymentCollected ?? this.paymentCollected,
    );
  }
}

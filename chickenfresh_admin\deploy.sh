#!/bin/bash

# chickenfresh Admin Production Deployment Script
# This script builds and deploys the Flutter web app to Firebase Hosting

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_ID="chickenfresh-e5b0f"
BUILD_DIR="build/web"
ENVIRONMENT=${1:-production}

echo -e "${BLUE}🚀 Starting chickenfresh Admin deployment...${NC}"
echo -e "${BLUE}Environment: ${ENVIRONMENT}${NC}"

# Check if Firebase CLI is installed
if ! command -v firebase &> /dev/null; then
    echo -e "${RED}❌ Firebase CLI is not installed. Please install it first:${NC}"
    echo -e "${YELLOW}npm install -g firebase-tools${NC}"
    exit 1
fi

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    echo -e "${RED}❌ Flutter is not installed. Please install Flutter first.${NC}"
    exit 1
fi

# Login to Firebase (if not already logged in)
echo -e "${BLUE}🔐 Checking Firebase authentication...${NC}"
if ! firebase projects:list &> /dev/null; then
    echo -e "${YELLOW}⚠️  Not logged in to Firebase. Please login:${NC}"
    firebase login
fi

# Set Firebase project
echo -e "${BLUE}📋 Setting Firebase project...${NC}"
firebase use $PROJECT_ID

# Clean previous builds
echo -e "${BLUE}🧹 Cleaning previous builds...${NC}"
flutter clean
rm -rf $BUILD_DIR

# Get dependencies
echo -e "${BLUE}📦 Getting Flutter dependencies...${NC}"
flutter pub get

# Run tests
echo -e "${BLUE}🧪 Running tests...${NC}"
flutter test || {
    echo -e "${RED}❌ Tests failed. Deployment aborted.${NC}"
    exit 1
}

# Build for web with environment variables
echo -e "${BLUE}🔨 Building Flutter web app for ${ENVIRONMENT}...${NC}"

if [ "$ENVIRONMENT" = "production" ]; then
    flutter build web \
        --release \
        --web-renderer html \
        --dart-define=ENVIRONMENT=production \
        --dart-define=ENABLE_ANALYTICS=true \
        --dart-define=ENABLE_CRASHLYTICS=true \
        --dart-define=ENABLE_PERFORMANCE_MONITORING=true \
        --dart-define=ENABLE_DEBUG_LOGGING=false
elif [ "$ENVIRONMENT" = "staging" ]; then
    flutter build web \
        --release \
        --web-renderer html \
        --dart-define=ENVIRONMENT=staging \
        --dart-define=ENABLE_ANALYTICS=true \
        --dart-define=ENABLE_CRASHLYTICS=true \
        --dart-define=ENABLE_PERFORMANCE_MONITORING=true \
        --dart-define=ENABLE_DEBUG_LOGGING=true
else
    flutter build web \
        --release \
        --web-renderer html \
        --dart-define=ENVIRONMENT=development \
        --dart-define=ENABLE_ANALYTICS=false \
        --dart-define=ENABLE_CRASHLYTICS=false \
        --dart-define=ENABLE_PERFORMANCE_MONITORING=false \
        --dart-define=ENABLE_DEBUG_LOGGING=true
fi

# Check if build was successful
if [ ! -d "$BUILD_DIR" ]; then
    echo -e "${RED}❌ Build failed. $BUILD_DIR directory not found.${NC}"
    exit 1
fi

# Deploy Firestore rules
echo -e "${BLUE}🔒 Deploying Firestore security rules...${NC}"
firebase deploy --only firestore:rules

# Deploy Storage rules
echo -e "${BLUE}🔒 Deploying Storage security rules...${NC}"
firebase deploy --only storage:rules

# Deploy to Firebase Hosting
echo -e "${BLUE}🌐 Deploying to Firebase Hosting...${NC}"
firebase deploy --only hosting

# Get the hosting URL
HOSTING_URL="https://$PROJECT_ID.web.app"

echo -e "${GREEN}✅ Deployment completed successfully!${NC}"
echo -e "${GREEN}🌐 Your app is live at: $HOSTING_URL${NC}"

# Optional: Open the deployed app
read -p "Do you want to open the deployed app in your browser? (y/n): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if command -v open &> /dev/null; then
        open $HOSTING_URL
    elif command -v xdg-open &> /dev/null; then
        xdg-open $HOSTING_URL
    elif command -v start &> /dev/null; then
        start $HOSTING_URL
    else
        echo -e "${YELLOW}⚠️  Could not open browser automatically. Please visit: $HOSTING_URL${NC}"
    fi
fi

echo -e "${BLUE}📋 Deployment Summary:${NC}"
echo -e "${BLUE}  Environment: ${ENVIRONMENT}${NC}"
echo -e "${BLUE}  Project ID: ${PROJECT_ID}${NC}"
echo -e "${BLUE}  Hosting URL: ${HOSTING_URL}${NC}"
echo -e "${BLUE}  Build Directory: ${BUILD_DIR}${NC}"

echo -e "${GREEN}🎉 Deployment completed successfully!${NC}"

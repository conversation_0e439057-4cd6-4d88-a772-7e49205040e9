import 'package:flutter/material.dart';

class ChickenWatermark extends StatelessWidget {
  final Widget child;
  final double opacity;
  final double size;
  final int count;

  const ChickenWatermark({
    super.key,
    required this.child,
    this.opacity = 0.05,
    this.size = 80,
    this.count = 12,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Watermark background
        Positioned.fill(
          child: CustomPaint(
            painter: ChickenWatermarkPainter(
              opacity: opacity,
              size: size,
              count: count,
            ),
          ),
        ),
        // Main content
        child,
      ],
    );
  }
}

class ChickenWatermarkPainter extends CustomPainter {
  final double opacity;
  final double size;
  final int count;

  ChickenWatermarkPainter({
    required this.opacity,
    required this.size,
    required this.count,
  });

  @override
  void paint(Canvas canvas, Size canvasSize) {
    // Calculate grid dimensions
    final cols = (canvasSize.width / (size * 1.5)).ceil();
    final rows = (canvasSize.height / (size * 1.5)).ceil();
    
    final paint = Paint()
      ..color = Colors.red.withOpacity(opacity)
      ..style = PaintingStyle.fill;

    // Draw chicken icons in a grid pattern
    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < cols; col++) {
        final x = col * size * 1.5 + (row.isEven ? 0 : size * 0.75);
        final y = row * size * 1.5;
        
        if (x < canvasSize.width && y < canvasSize.height) {
          _drawChickenIcon(canvas, Offset(x, y), size, paint);
        }
      }
    }
  }

  void _drawChickenIcon(Canvas canvas, Offset position, double iconSize, Paint paint) {
    final center = position + Offset(iconSize / 2, iconSize / 2);
    
    // Draw simple chicken silhouette
    final path = Path();
    
    // Chicken body (oval)
    final bodyRect = Rect.fromCenter(
      center: center + const Offset(0, 5),
      width: iconSize * 0.6,
      height: iconSize * 0.8,
    );
    path.addOval(bodyRect);
    
    // Chicken head (circle)
    final headCenter = center + Offset(0, -iconSize * 0.25);
    path.addOval(Rect.fromCenter(
      center: headCenter,
      width: iconSize * 0.4,
      height: iconSize * 0.4,
    ));
    
    // Chicken beak (triangle)
    final beakPath = Path();
    beakPath.moveTo(headCenter.dx - iconSize * 0.25, headCenter.dy);
    beakPath.lineTo(headCenter.dx - iconSize * 0.35, headCenter.dy - iconSize * 0.05);
    beakPath.lineTo(headCenter.dx - iconSize * 0.35, headCenter.dy + iconSize * 0.05);
    beakPath.close();
    path.addPath(beakPath, Offset.zero);
    
    // Chicken tail (small triangle)
    final tailPath = Path();
    final tailCenter = center + Offset(iconSize * 0.3, -iconSize * 0.1);
    tailPath.moveTo(tailCenter.dx, tailCenter.dy);
    tailPath.lineTo(tailCenter.dx + iconSize * 0.15, tailCenter.dy - iconSize * 0.1);
    tailPath.lineTo(tailCenter.dx + iconSize * 0.15, tailCenter.dy + iconSize * 0.1);
    tailPath.close();
    path.addPath(tailPath, Offset.zero);
    
    canvas.drawPath(path, paint);
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

// Alternative watermark using the actual chicken image
class ChickenImageWatermark extends StatelessWidget {
  final Widget child;
  final double opacity;
  final double size;

  const ChickenImageWatermark({
    super.key,
    required this.child,
    this.opacity = 0.08,
    this.size = 100,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Watermark background with actual chicken images
        Positioned.fill(
          child: LayoutBuilder(
            builder: (context, constraints) {
              final cols = (constraints.maxWidth / (size * 1.5)).ceil();
              final rows = (constraints.maxHeight / (size * 1.5)).ceil();
              
              return Stack(
                children: [
                  for (int row = 0; row < rows; row++)
                    for (int col = 0; col < cols; col++)
                      Positioned(
                        left: col * size * 1.5 + (row.isEven ? 0 : size * 0.75),
                        top: row * size * 1.5,
                        child: Opacity(
                          opacity: opacity,
                          child: Transform.rotate(
                            angle: (row + col) * 0.1, // Slight rotation for variety
                            child: SizedBox(
                              width: size,
                              height: size,
                              child: Image.asset(
                                'assets/images/chicken.jpg',
                                fit: BoxFit.cover,
                                errorBuilder: (context, error, stackTrace) {
                                  return Icon(
                                    Icons.restaurant,
                                    size: size * 0.8,
                                    color: Colors.red.withOpacity(opacity),
                                  );
                                },
                              ),
                            ),
                          ),
                        ),
                      ),
                ],
              );
            },
          ),
        ),
        // Main content
        child,
      ],
    );
  }
}

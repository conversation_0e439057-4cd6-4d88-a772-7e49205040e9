import 'package:flutter/material.dart';
import '../services/notification_service.dart';
import '../widgets/chicken_watermark.dart';
import 'notification_demo_screen.dart';

class NotificationSettingsScreen extends StatefulWidget {
  const NotificationSettingsScreen({super.key});

  @override
  State<NotificationSettingsScreen> createState() => _NotificationSettingsScreenState();
}

class _NotificationSettingsScreenState extends State<NotificationSettingsScreen> {
  Map<String, bool> _settings = {};
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _loadSettings();
  }

  Future<void> _loadSettings() async {
    final settings = await NotificationService.getNotificationSettings();
    setState(() {
      _settings = settings;
      _isLoading = false;
    });
  }

  Future<void> _saveSettings() async {
    await NotificationService.saveNotificationSettings(_settings);
    if (mounted) {
      NotificationService.showSuccessNotification('Notification settings saved!');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey.shade50,
      appBar: AppBar(
        title: const Text('Notification Settings'),
        backgroundColor: Colors.red.shade400,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            icon: const Icon(Icons.play_arrow),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const NotificationDemoScreen(),
                ),
              );
            },
            tooltip: 'Demo',
          ),
          IconButton(
            icon: const Icon(Icons.save),
            onPressed: _saveSettings,
          ),
        ],
      ),
      body: ChickenImageWatermark(
        child: _isLoading
            ? const Center(child: CircularProgressIndicator())
            : SingleChildScrollView(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Header
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          colors: [Colors.blue.shade50, Colors.blue.shade100],
                        ),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: Colors.blue.shade200),
                      ),
                      child: Row(
                        children: [
                          Icon(Icons.notifications_active, color: Colors.blue.shade600, size: 28),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Manage Notifications',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                    color: Colors.blue.shade800,
                                  ),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Choose which notifications you want to receive',
                                  style: TextStyle(
                                    fontSize: 14,
                                    color: Colors.blue.shade700,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 24),

                    // Notification Categories
                    _buildNotificationCategory(
                      'Orders',
                      'orders',
                      'Get notified about order status updates',
                      Icons.shopping_bag,
                      Colors.green,
                    ),
                    const SizedBox(height: 16),

                    _buildNotificationCategory(
                      'Delivery',
                      'delivery',
                      'Track your delivery status and updates',
                      Icons.local_shipping,
                      Colors.orange,
                    ),
                    const SizedBox(height: 16),

                    _buildNotificationCategory(
                      'Offers & Promotions',
                      'offers',
                      'Receive special offers and discount notifications',
                      Icons.local_offer,
                      Colors.purple,
                    ),
                    const SizedBox(height: 16),

                    _buildNotificationCategory(
                      'New Products',
                      'products',
                      'Be the first to know about new products',
                      Icons.restaurant,
                      Colors.red,
                    ),
                    const SizedBox(height: 16),

                    _buildNotificationCategory(
                      'General',
                      'general',
                      'App updates and general announcements',
                      Icons.info,
                      Colors.blue,
                    ),
                    const SizedBox(height: 32),

                    // Test Notifications Section
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.white,
                        borderRadius: BorderRadius.circular(12),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.grey.withValues(alpha: 0.1),
                            blurRadius: 8,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Icon(Icons.bug_report, color: Colors.grey.shade600),
                              const SizedBox(width: 8),
                              const Text(
                                'Test Notifications',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          const Text(
                            'Test different types of notifications to see how they appear',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.grey,
                            ),
                          ),
                          const SizedBox(height: 16),
                          Wrap(
                            spacing: 8,
                            runSpacing: 8,
                            children: [
                              _buildTestButton(
                                'Order Update',
                                () => NotificationService.showOrderNotification(
                                  orderId: 'TEST123',
                                  status: 'confirmed',
                                  message: 'Your test order has been confirmed!',
                                ),
                                Colors.green,
                              ),
                              _buildTestButton(
                                'Delivery',
                                () => NotificationService.showDeliveryNotification(
                                  orderId: 'TEST123',
                                  status: 'out for delivery',
                                  estimatedTime: '15 minutes',
                                ),
                                Colors.orange,
                              ),
                              _buildTestButton(
                                'Offer',
                                () => NotificationService.showOfferNotification(
                                  title: '50% Off!',
                                  description: 'Special discount on all chicken products',
                                  offerId: 'OFFER123',
                                ),
                                Colors.purple,
                              ),
                              _buildTestButton(
                                'Success',
                                () => NotificationService.showSuccessNotification(
                                  'This is a test success notification!',
                                ),
                                Colors.green,
                              ),
                              _buildTestButton(
                                'Error',
                                () => NotificationService.showErrorNotification(
                                  'This is a test error notification!',
                                ),
                                Colors.red,
                              ),
                              _buildTestButton(
                                'Info',
                                () => NotificationService.showInfoNotification(
                                  'This is a test info notification!',
                                ),
                                Colors.blue,
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    const SizedBox(height: 32),

                    // Clear All Notifications
                    SizedBox(
                      width: double.infinity,
                      child: OutlinedButton.icon(
                        onPressed: () async {
                          await NotificationService.cancelAllNotifications();
                          if (mounted) {
                            NotificationService.showInfoNotification(
                              'All notifications cleared!',
                            );
                          }
                        },
                        icon: const Icon(Icons.clear_all),
                        label: const Text('Clear All Notifications'),
                        style: OutlinedButton.styleFrom(
                          side: BorderSide(color: Colors.red.shade400),
                          padding: const EdgeInsets.symmetric(vertical: 16),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(12),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
      ),
    );
  }

  Widget _buildNotificationCategory(
    String title,
    String key,
    String description,
    IconData icon,
    MaterialColor color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: color.shade50,
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              icon,
              color: color.shade600,
              size: 24,
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    fontSize: 14,
                    color: Colors.grey.shade600,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: _settings[key] ?? true,
            onChanged: (value) {
              setState(() {
                _settings[key] = value;
              });
            },
            activeColor: color.shade600,
          ),
        ],
      ),
    );
  }

  Widget _buildTestButton(String label, VoidCallback onPressed, MaterialColor color) {
    return ElevatedButton(
      onPressed: onPressed,
      style: ElevatedButton.styleFrom(
        backgroundColor: color.shade100,
        foregroundColor: color.shade700,
        elevation: 0,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
      child: Text(
        label,
        style: const TextStyle(fontSize: 12),
      ),
    );
  }
}

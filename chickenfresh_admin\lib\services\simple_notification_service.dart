import 'dart:async';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

/// Simplified in-memory notification service
class SimpleNotificationService {
  static final SimpleNotificationService _instance = SimpleNotificationService._internal();
  factory SimpleNotificationService() => _instance;
  SimpleNotificationService._internal();

  final List<SimpleNotification> _activeNotifications = [];
  final StreamController<List<SimpleNotification>> _notificationController = 
      StreamController<List<SimpleNotification>>.broadcast();

  /// Stream of active notifications
  Stream<List<SimpleNotification>> get notificationStream => _notificationController.stream;

  /// Initialize notification service
  void initialize() {
    // Add some sample notifications for demonstration
    _addSampleNotifications();
  }

  /// Dispose resources
  void dispose() {
    _notificationController.close();
  }

  /// Add notification to queue (in-memory only)
  void addNotification(SimpleNotification notification) {
    // Check for duplicates
    if (!_activeNotifications.any((n) => n.id == notification.id)) {
      _activeNotifications.add(notification);
      _notificationController.add(List.from(_activeNotifications));
      
      // Auto-remove low priority notifications after 30 seconds
      if (notification.priority == NotificationPriority.low) {
        Timer(const Duration(seconds: 30), () {
          removeNotification(notification.id);
        });
      }
    }
  }

  /// Remove notification from memory
  void removeNotification(String notificationId) {
    _activeNotifications.removeWhere((n) => n.id == notificationId);
    _notificationController.add(List.from(_activeNotifications));
  }

  /// Clear all notifications (in-memory only)
  void clearAllNotifications() {
    _activeNotifications.clear();
    _notificationController.add(List.from(_activeNotifications));
  }

  /// Add sample notifications for demonstration
  void _addSampleNotifications() {
    final sampleNotifications = [
      SimpleNotification(
        id: 'welcome_${DateTime.now().millisecondsSinceEpoch}',
        type: NotificationType.systemAlert,
        title: 'Welcome to Admin Panel',
        message: 'All systems are running smoothly. You will receive real-time notifications here.',
        priority: NotificationPriority.low,
        createdAt: DateTime.now(),
      ),
    ];
    
    for (final notification in sampleNotifications) {
      addNotification(notification);
    }
  }

  /// Create new order notification
  void notifyNewOrder(String orderNumber, String customerName, double amount) {
    final notification = SimpleNotification(
      id: 'new_order_${DateTime.now().millisecondsSinceEpoch}',
      type: NotificationType.newOrder,
      title: 'New Order Received',
      message: 'Order $orderNumber from $customerName - ₹${amount.toStringAsFixed(2)}',
      priority: NotificationPriority.high,
      createdAt: DateTime.now(),
    );
    addNotification(notification);
  }

  /// Create order delivered notification
  void notifyOrderDelivered(String orderNumber, String customerName) {
    final notification = SimpleNotification(
      id: 'delivered_${DateTime.now().millisecondsSinceEpoch}',
      type: NotificationType.orderDelivered,
      title: 'Order Delivered',
      message: 'Order $orderNumber has been delivered to $customerName',
      priority: NotificationPriority.medium,
      createdAt: DateTime.now(),
    );
    addNotification(notification);
  }

  /// Create stock alert notification
  void notifyLowStock(String productName, int quantity) {
    final notification = SimpleNotification(
      id: 'stock_alert_${DateTime.now().millisecondsSinceEpoch}',
      type: NotificationType.stockAlert,
      title: 'Low Stock Alert',
      message: '$productName is running low (only $quantity left)',
      priority: NotificationPriority.high,
      createdAt: DateTime.now(),
    );
    addNotification(notification);
  }

  /// Create system alert notification
  void notifySystemAlert(String title, String message, {NotificationPriority priority = NotificationPriority.medium}) {
    final notification = SimpleNotification(
      id: 'system_${DateTime.now().millisecondsSinceEpoch}',
      type: NotificationType.systemAlert,
      title: title,
      message: message,
      priority: priority,
      createdAt: DateTime.now(),
    );
    addNotification(notification);
  }

  /// Show popup notification
  static void showPopupNotification(
    BuildContext context, {
    required String title,
    required String message,
    IconData? icon,
    Color? backgroundColor,
    Duration duration = const Duration(seconds: 4),
  }) {
    final overlay = Overlay.of(context);
    late OverlayEntry overlayEntry;

    overlayEntry = OverlayEntry(
      builder: (context) => Positioned(
        top: MediaQuery.of(context).padding.top + 10,
        left: 16,
        right: 16,
        child: Material(
          color: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: backgroundColor ?? Colors.green.shade600,
              borderRadius: BorderRadius.circular(12),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.2),
                  blurRadius: 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: Row(
              children: [
                if (icon != null) ...[
                  Icon(icon, color: Colors.white, size: 24),
                  const SizedBox(width: 12),
                ],
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Text(
                        title,
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        message,
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                IconButton(
                  onPressed: () => overlayEntry.remove(),
                  icon: const Icon(Icons.close, color: Colors.white),
                ),
              ],
            ),
          ),
        ),
      ),
    );

    overlay.insert(overlayEntry);

    // Auto remove after duration
    Future.delayed(duration, () {
      if (overlayEntry.mounted) {
        overlayEntry.remove();
      }
    });
  }
}

/// Notification data models
enum NotificationType {
  newOrder,
  orderFollowUp,
  orderDelivered,
  stockAlert,
  systemAlert,
}

enum NotificationPriority {
  low,
  medium,
  high,
  critical,
}

class SimpleNotification {
  final String id;
  final NotificationType type;
  final String title;
  final String message;
  final NotificationPriority priority;
  final DateTime createdAt;

  SimpleNotification({
    required this.id,
    required this.type,
    required this.title,
    required this.message,
    required this.priority,
    required this.createdAt,
  });
}

import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'auth_service.dart';

class SettingsException implements Exception {
  final String message;
  SettingsException(this.message);
  
  @override
  String toString() => message;
}

class SettingsService {
  static final SettingsService _instance = SettingsService._internal();
  factory SettingsService() => _instance;
  SettingsService._internal();

  final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  final AuthService _authService = AuthService();

  // Get UPI settings
  Future<Map<String, dynamic>?> getUpiSettings() async {
    try {
      final doc = await _firestore.collection('settings').doc('payment').get();
      
      if (doc.exists) {
        return doc.data() as Map<String, dynamic>;
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting UPI settings: $e');
      }
      throw SettingsException('Failed to get UPI settings: $e');
    }
  }

  // Update UPI settings with PIN protection
  Future<void> updateUpiSettings({
    required String upiId,
    required String password, // Keep parameter name for compatibility
    String? currentPassword, // Optional for first-time setup
  }) async {
    try {
      if (!_authService.isAuthenticated) {
        throw SettingsException('Authentication required');
      }

      // Verify current password by checking admin credentials
      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw SettingsException('User not authenticated');
      }

      // Check if this is first-time setup or PIN update
      final existingSettings = await getUpiSettings();
      final bool isFirstTimeSetup = existingSettings == null || existingSettings['password'] == null;

      // For existing settings, verify current PIN
      if (!isFirstTimeSetup) {
        if (currentPassword == null || currentPassword.isEmpty) {
          throw SettingsException('Current PIN is required for updates');
        }
        if (existingSettings['password'] != currentPassword) {
          throw SettingsException('Current PIN is incorrect');
        }
      }

      // Update UPI settings
      await _firestore.collection('settings').doc('payment').set({
        'upiId': upiId,
        'password': password,
        'updatedAt': FieldValue.serverTimestamp(),
        'updatedBy': currentUser.uid,
      }, SetOptions(merge: true));

      if (kDebugMode) {
        print('UPI settings updated successfully');
      }
    } catch (e) {
      if (e is SettingsException) rethrow;
      throw SettingsException('Failed to update UPI settings: $e');
    }
  }

  // Verify UPI settings password
  Future<bool> verifyUpiPassword(String password) async {
    try {
      final settings = await getUpiSettings();
      
      if (settings == null || settings['password'] == null) {
        return false;
      }
      
      return settings['password'] == password;
    } catch (e) {
      if (kDebugMode) {
        print('Error verifying UPI password: $e');
      }
      return false;
    }
  }

  // Get UPI ID (public method for delivery app)
  Future<String?> getUpiId() async {
    try {
      final settings = await getUpiSettings();
      return settings?['upiId'] as String?;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting UPI ID: $e');
      }
      return null;
    }
  }

  // Initialize default settings if they don't exist
  Future<void> initializeDefaultSettings() async {
    try {
      final doc = await _firestore.collection('settings').doc('payment').get();
      
      if (!doc.exists) {
        await _firestore.collection('settings').doc('payment').set({
          'upiId': null,
          'password': '1234', // Default 4-digit PIN
          'createdAt': FieldValue.serverTimestamp(),
        });
        
        if (kDebugMode) {
          print('Default payment settings initialized');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing default settings: $e');
      }
    }
  }

  // Get all app settings
  Future<Map<String, dynamic>> getAllSettings() async {
    try {
      final paymentSettings = await getUpiSettings();
      final notificationSettings = await getNotificationSettings();

      return {
        'payment': paymentSettings ?? {},
        'notifications': notificationSettings ?? {},
      };
    } catch (e) {
      if (kDebugMode) {
        print('Error getting all settings: $e');
      }
      return {};
    }
  }

  // Get notification settings
  Future<Map<String, dynamic>?> getNotificationSettings() async {
    try {
      if (!_authService.isAuthenticated) {
        return null;
      }

      final doc = await _firestore.collection('settings').doc('notifications').get();

      if (doc.exists) {
        return doc.data();
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting notification settings: $e');
      }
      throw SettingsException('Failed to get notification settings: $e');
    }
  }

  // Update notification settings
  Future<void> updateNotificationSettings(Map<String, dynamic> settings) async {
    try {
      if (!_authService.isAuthenticated) {
        throw SettingsException('Authentication required');
      }

      final currentUser = _authService.currentUser;
      if (currentUser == null) {
        throw SettingsException('User not authenticated');
      }

      // Update notification settings
      await _firestore.collection('settings').doc('notifications').set({
        ...settings,
        'updatedAt': FieldValue.serverTimestamp(),
        'updatedBy': currentUser.uid,
      }, SetOptions(merge: true));

      if (kDebugMode) {
        print('Notification settings updated successfully');
      }
    } catch (e) {
      if (e is SettingsException) rethrow;
      throw SettingsException('Failed to update notification settings: $e');
    }
  }
}

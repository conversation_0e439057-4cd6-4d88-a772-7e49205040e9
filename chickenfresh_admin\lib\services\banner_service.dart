import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/banner_model.dart';

class BannerService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static const String _collection = 'banners';

  // Get all active banners ordered by their order field
  static Stream<List<BannerModel>> getActiveBanners() {
    return _firestore
        .collection(_collection)
        .where('isActive', isEqualTo: true)
        .orderBy('order')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => BannerModel.fromFirestore(doc))
            .toList());
  }

  // Get all banners (for admin)
  static Stream<List<BannerModel>> getAllBanners() {
    return _firestore
        .collection(_collection)
        .orderBy('order')
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => BannerModel.fromFirestore(doc))
            .toList());
  }

  // Add a new banner
  static Future<String> addBanner(BannerModel banner) async {
    try {
      final docRef = await _firestore.collection(_collection).add(banner.toFirestore());
      return docRef.id;
    } catch (e) {
      throw Exception('Failed to add banner: $e');
    }
  }

  // Update an existing banner
  static Future<void> updateBanner(String bannerId, BannerModel banner) async {
    try {
      await _firestore
          .collection(_collection)
          .doc(bannerId)
          .update(banner.copyWith(updatedAt: DateTime.now()).toFirestore());
    } catch (e) {
      throw Exception('Failed to update banner: $e');
    }
  }

  // Delete a banner
  static Future<void> deleteBanner(String bannerId) async {
    try {
      await _firestore.collection(_collection).doc(bannerId).delete();
    } catch (e) {
      throw Exception('Failed to delete banner: $e');
    }
  }

  // Toggle banner active status
  static Future<void> toggleBannerStatus(String bannerId, bool isActive) async {
    try {
      await _firestore.collection(_collection).doc(bannerId).update({
        'isActive': isActive,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('Failed to toggle banner status: $e');
    }
  }

  // Update banner order
  static Future<void> updateBannerOrder(String bannerId, int newOrder) async {
    try {
      await _firestore.collection(_collection).doc(bannerId).update({
        'order': newOrder,
        'updatedAt': Timestamp.fromDate(DateTime.now()),
      });
    } catch (e) {
      throw Exception('Failed to update banner order: $e');
    }
  }

  // Get banner by ID
  static Future<BannerModel?> getBannerById(String bannerId) async {
    try {
      final doc = await _firestore.collection(_collection).doc(bannerId).get();
      if (doc.exists) {
        return BannerModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      throw Exception('Failed to get banner: $e');
    }
  }
}
